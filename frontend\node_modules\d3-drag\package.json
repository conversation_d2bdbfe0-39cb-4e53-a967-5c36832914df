{"_from": "d3-drag@2", "_id": "d3-drag@2.0.0", "_inBundle": false, "_integrity": "sha512-g9y9WbMnF5uqB9qKqwIIa/921RYWzlUDv9Jl1/yONQwxbOfszAWTCm8u7HOTgJgRDXiRZN56cHT9pd24dmXs8w==", "_location": "/d3-drag", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-drag@2", "name": "d3-drag", "escapedName": "d3-drag", "rawSpec": "2", "saveSpec": null, "fetchSpec": "2"}, "_requiredBy": ["/d3-zoom"], "_resolved": "https://registry.npmjs.org/d3-drag/-/d3-drag-2.0.0.tgz", "_shasum": "9eaf046ce9ed1c25c88661911c1d5a4d8eb7ea6d", "_spec": "d3-drag@2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\d3-zoom", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-drag/issues"}, "bundleDependencies": false, "dependencies": {"d3-dispatch": "1 - 2", "d3-selection": "2"}, "deprecated": false, "description": "Drag and drop SVG, HTML or Canvas using mouse or touch input.", "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-drag/", "jsdelivr": "dist/d3-drag.min.js", "keywords": ["d3", "d3-module", "drag", "behavior", "interaction"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-drag.js", "module": "src/index.js", "name": "d3-drag", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-drag.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src"}, "sideEffects": false, "unpkg": "dist/d3-drag.min.js", "version": "2.0.0"}