{"_from": "@types/d3-interpolate@^2", "_id": "@types/d3-interpolate@2.0.5", "_inBundle": false, "_integrity": "sha512-UINE41RDaUMbulp+bxQMDnhOi51rh5lA2dG+dWZU0UY/IwQiG/u2x8TfnWYU9+xwGdXsJoAvrBYUEQl0r91atg==", "_location": "/@types/d3-zoom/@types/d3-interpolate", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/d3-interpolate@^2", "name": "@types/d3-interpolate", "escapedName": "@types%2fd3-interpolate", "scope": "@types", "rawSpec": "^2", "saveSpec": null, "fetchSpec": "^2"}, "_requiredBy": ["/@types/d3-zoom"], "_resolved": "https://registry.npmjs.org/@types/d3-interpolate/-/d3-interpolate-2.0.5.tgz", "_shasum": "063a3b81cda9f74660cc8ac4533854d5b45ff12f", "_spec": "@types/d3-interpolate@^2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\@types\\d3-zoom", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "denis<PERSON>", "url": "https://github.com/denisname"}, {"name": "<PERSON>", "url": "https://github.com/Methuselah96"}], "dependencies": {"@types/d3-color": "^2"}, "deprecated": false, "description": "TypeScript definitions for d3-interpolate", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-interpolate", "license": "MIT", "main": "", "name": "@types/d3-interpolate", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-interpolate"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "e1814769157f257f4f2ee630107886281f719254050d1ccc7cdfe816ddd9b747", "version": "2.0.5"}