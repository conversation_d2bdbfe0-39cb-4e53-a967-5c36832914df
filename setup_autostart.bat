@echo off
echo Setting up autostart for GlobalRoamer Scraping Service...

REM Créer un raccourci dans le dossier de démarrage
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = oWS.SpecialFolders("Startup") ^& "\StartScrapingService.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%~dp0start_scraping_service.bat" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%~dp0" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

cscript CreateShortcut.vbs
del CreateShortcut.vbs

echo Setup complete! The service will now start automatically with Windows.
echo.
echo Starting the service now...
call start_scraping_service.bat 