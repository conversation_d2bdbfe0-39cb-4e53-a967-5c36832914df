#!/usr/bin/env python3
"""
Test complet de génération et d'import de données avec toutes les colonnes
"""

import os
import sys
from dotenv import load_dotenv

# Charger les variables d'environnement
env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(env_path)

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from automation_service import AutomationService

def test_complete_import():
    """Test complet avec toutes les colonnes"""
    print("🧪 TEST COMPLET D'IMPORT AVEC TOUTES LES COLONNES")
    print("=" * 60)
    
    try:
        # Créer le service d'automatisation
        service = AutomationService()
        
        print(f"🔑 Username: {service.username}")
        print(f"🔑 Password: {'*' * len(service.password) if service.password else 'None'}")
        
        # Générer des données de test
        print("\n📊 Génération de données de test...")
        test_files = service._generate_test_csv_files(days_back=1)
        
        if test_files:
            print(f"✅ {len(test_files)} fichier(s) généré(s)")
            
            # Examiner le contenu du premier fichier
            import pandas as pd
            df = pd.read_csv(test_files[0])
            print(f"\n📋 Fichier: {test_files[0]}")
            print(f"📊 Nombre de lignes: {len(df)}")
            print(f"📊 Nombre de colonnes: {len(df.columns)}")
            print(f"📊 Colonnes: {list(df.columns)}")
            
            # Afficher quelques exemples de données
            print(f"\n📝 Aperçu des données:")
            print(df.head(2).to_string())
            
            # Tester l'import
            print(f"\n📥 Test d'import dans la base de données...")
            from csv_importer import CSVImporter
            importer = CSVImporter()
            
            success = importer.import_csv_file(test_files[0])
            
            if success:
                print("✅ Import réussi!")
                
                # Vérifier les données dans la base
                stats = importer.get_import_stats()
                print(f"\n📊 Statistiques d'import:")
                for key, value in stats.items():
                    print(f"  - {key}: {value}")
                    
            else:
                print("❌ Échec de l'import")
                
        else:
            print("❌ Aucun fichier généré")
            
        return len(test_files) > 0
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_structure():
    """Vérifier la structure de la base de données"""
    print("\n🔍 VÉRIFICATION DE LA STRUCTURE DE LA BASE DE DONNÉES")
    print("=" * 60)
    
    try:
        import mysql.connector
        from dotenv import load_dotenv
        
        # Charger les variables d'environnement
        env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
        load_dotenv(env_path)
        
        # Connexion à la base de données
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', ''),
            database=os.getenv('DB_NAME', 'kpi')
        )
        
        cursor = connection.cursor()
        
        # Compter les enregistrements
        cursor.execute("SELECT COUNT(*) FROM steeringofroaming")
        count = cursor.fetchone()[0]
        print(f"📊 Nombre total d'enregistrements: {count}")
        
        # Vérifier les derniers enregistrements
        cursor.execute("""
            SELECT TCName, Verdict, a_location_country, a_NetworkType, Timestamp 
            FROM steeringofroaming 
            ORDER BY Timestamp DESC 
            LIMIT 5
        """)
        
        recent_records = cursor.fetchall()
        print(f"\n📋 Derniers enregistrements:")
        for record in recent_records:
            print(f"  - {record[0]} | {record[1]} | {record[2]} | {record[3]} | {record[4]}")
        
        # Vérifier la distribution par pays
        cursor.execute("""
            SELECT a_location_country, COUNT(*) as count 
            FROM steeringofroaming 
            WHERE a_location_country IS NOT NULL 
            GROUP BY a_location_country 
            ORDER BY count DESC 
            LIMIT 10
        """)
        
        country_stats = cursor.fetchall()
        print(f"\n🌍 Distribution par pays:")
        for country, count in country_stats:
            print(f"  - {country}: {count} enregistrements")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 TEST COMPLET DU SYSTÈME D'IMPORT")
    print("=" * 60)
    
    # Test de génération et d'import
    import_success = test_complete_import()
    
    # Vérification de la base de données
    db_success = check_database_structure()
    
    # Résumé
    print(f"\n📋 RÉSUMÉ:")
    print(f"  - Import: {'✅ RÉUSSI' if import_success else '❌ ÉCHOUÉ'}")
    print(f"  - Base de données: {'✅ OK' if db_success else '❌ PROBLÈME'}")
    
    overall_success = import_success and db_success
    print(f"\n{'🎉 TOUS LES TESTS RÉUSSIS' if overall_success else '⚠️ CERTAINS TESTS ONT ÉCHOUÉ'}")
    
    return overall_success

if __name__ == "__main__":
    main()
