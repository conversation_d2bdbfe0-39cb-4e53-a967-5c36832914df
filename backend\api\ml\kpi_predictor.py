import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest, RandomForestRegressor
from sklearn.model_selection import train_test_split
import joblib
import os
from typing import List, Dict, Any, Tuple

class KPIAnomalyDetector:
    def __init__(self):
        self.scaler = StandardScaler()
        self.isolation_forest = IsolationForest(contamination=0.1, random_state=42)
        self.predictor = RandomForestRegressor(n_estimators=100, random_state=42)
        self.model_path = "models"
        self.z_score_threshold = 3.0  # Seuil standard pour la méthode Z-score
        self.ma_window = 10  # Fenêtre pour la moyenne mobile
        self.ma_std_multiplier = 2.0  # Multiplicateur d'écart-type pour la moyenne mobile
        
        # Seuils pour l'analyse des causes
        self.kpi_thresholds = {
            "attach_success": {
                "critical_drop": -15,  # % de baisse considéré comme critique
                "warning_drop": -10,   # % de baisse consid<PERSON><PERSON> comme avertissement
                "min_acceptable": 85    # % minimum acceptable
            },
            "drop_rate": {
                "critical": 10,        # % considéré comme critique
                "warning": 5           # % considéré comme avertissement
            },
            "latency": {
                "critical": 2000,      # ms considéré comme critique
                "warning": 1000        # ms considéré comme avertissement
            }
        }
        
        os.makedirs(self.model_path, exist_ok=True)

    def prepare_sequences(self, data, sequence_length=10):
        """Prépare les séquences pour la prédiction"""
        X, y = [], []
        numeric_data = data.select_dtypes(include=['float64', 'int64'])
        
        for i in range(len(data) - sequence_length):
            X.append(numeric_data.iloc[i:i + sequence_length].values.flatten())
            y.append(numeric_data.iloc[i + sequence_length].values)
        
        return np.array(X), np.array(y)

    def detect_anomalies_zscore(self, data: pd.DataFrame) -> List[int]:
        """Détecte les anomalies en utilisant la méthode Z-score"""
        numeric_features = data.select_dtypes(include=['float64', 'int64']).columns
        anomalies = []
        
        for feature in numeric_features:
            values = data[feature].values
            z_scores = np.abs((values - np.mean(values)) / np.std(values))
            anomaly_indices = np.where(z_scores > self.z_score_threshold)[0]
            anomalies.extend(anomaly_indices)
        
        return list(set(anomalies))  # Retourne les indices uniques

    def detect_anomalies_moving_average(self, data: pd.DataFrame) -> List[int]:
        """Détecte les anomalies en utilisant la moyenne mobile et un seuil dynamique"""
        numeric_features = data.select_dtypes(include=['float64', 'int64']).columns
        anomalies = []
        
        for feature in numeric_features:
            values = data[feature].values
            # Calcul de la moyenne mobile
            ma = pd.Series(values).rolling(window=self.ma_window, center=True).mean()
            # Calcul de l'écart-type mobile
            mstd = pd.Series(values).rolling(window=self.ma_window, center=True).std()
            
            # Définition des seuils dynamiques
            upper_bound = ma + (mstd * self.ma_std_multiplier)
            lower_bound = ma - (mstd * self.ma_std_multiplier)
            
            # Identification des anomalies
            anomaly_indices = np.where(
                (values > upper_bound) | 
                (values < lower_bound)
            )[0]
            
            anomalies.extend(anomaly_indices)
        
        return list(set(anomalies))  # Retourne les indices uniques

    def get_consensus_anomalies(self, data: pd.DataFrame, methods_results: Dict[str, List[int]], min_consensus: int = 2) -> List[int]:
        """
        Identifie les anomalies détectées par au moins min_consensus méthodes
        """
        all_indices = set()
        consensus_counts = {}
        
        # Compte combien de méthodes ont détecté chaque point comme anomalie
        for method_name, indices in methods_results.items():
            for idx in indices:
                consensus_counts[idx] = consensus_counts.get(idx, 0) + 1
                all_indices.add(idx)
        
        # Retourne les indices qui ont été détectés par au moins min_consensus méthodes
        consensus_anomalies = [idx for idx in all_indices if consensus_counts[idx] >= min_consensus]
        return sorted(consensus_anomalies)

    def analyze_cause(self, data: pd.DataFrame, idx: int, window: int = 5) -> Dict[str, Any]:
        """
        Analyse les causes probables d'une anomalie en examinant les variations des KPIs
        """
        causes = []
        details = {}
        severity_score = 0
        
        # Vérifier si nous avons assez de données historiques
        start_idx = max(0, idx - window)
        end_idx = min(len(data), idx + 1)
        
        if "attach_success" in data.columns:
            current_attach = data["attach_success"].iloc[idx]
            if start_idx < idx:
                prev_attach = data["attach_success"].iloc[start_idx:idx].mean()
                attach_change = ((current_attach - prev_attach) / prev_attach) * 100
                
                details["attach_success"] = {
                    "current": current_attach,
                    "previous_avg": prev_attach,
                    "change_percent": attach_change
                }
                
                if attach_change <= self.kpi_thresholds["attach_success"]["critical_drop"]:
                    causes.append("Severe drop in attachment success rate")
                    severity_score += 3
                elif attach_change <= self.kpi_thresholds["attach_success"]["warning_drop"]:
                    causes.append("Significant decrease in attachment success rate")
                    severity_score += 2
                elif current_attach < self.kpi_thresholds["attach_success"]["min_acceptable"]:
                    causes.append("Attachment success rate below acceptable threshold")
                    severity_score += 1

        if "drop_rate" in data.columns:
            current_drop = data["drop_rate"].iloc[idx]
            if start_idx < idx:
                prev_drop = data["drop_rate"].iloc[start_idx:idx].mean()
                drop_change = current_drop - prev_drop
                
                details["drop_rate"] = {
                    "current": current_drop,
                    "previous_avg": prev_drop,
                    "change": drop_change
                }
                
                if current_drop >= self.kpi_thresholds["drop_rate"]["critical"]:
                    causes.append("Critical increase in drop rate")
                    severity_score += 3
                elif current_drop >= self.kpi_thresholds["drop_rate"]["warning"]:
                    causes.append("Elevated drop rate")
                    severity_score += 2

        if "latency" in data.columns:
            current_latency = data["latency"].iloc[idx]
            if start_idx < idx:
                prev_latency = data["latency"].iloc[start_idx:idx].mean()
                latency_change = ((current_latency - prev_latency) / prev_latency) * 100
                
                details["latency"] = {
                    "current": current_latency,
                    "previous_avg": prev_latency,
                    "change_percent": latency_change
                }
                
                if current_latency >= self.kpi_thresholds["latency"]["critical"]:
                    causes.append("Critical latency detected")
                    severity_score += 3
                elif current_latency >= self.kpi_thresholds["latency"]["warning"]:
                    causes.append("High latency")
                    severity_score += 2
                elif latency_change > 50:  # Augmentation soudaine de plus de 50%
                    causes.append("Sudden increase in latency")
                    severity_score += 1

        # Analyse des corrélations entre métriques
        if len(causes) > 1:
            if "Critical increase in drop rate" in causes and "Severe drop in attachment success rate" in causes:
                causes.append("Network congestion or capacity issues detected")
                severity_score += 1
            elif "Critical latency detected" in causes and "Elevated drop rate" in causes:
                causes.append("Possible network performance degradation")
                severity_score += 1

        return {
            "causes": causes,
            "details": details,
            "severity_score": severity_score,
            "primary_cause": causes[0] if causes else "Unknown cause"
        }

    def train(self, data: pd.DataFrame):
        """Entraîne les modèles de détection d'anomalies"""
        # Préparation des données
        numeric_features = data.select_dtypes(include=['float64', 'int64']).columns
        X = data[numeric_features]
        
        # Normalisation
        X_scaled = self.scaler.fit_transform(X)
        
        # Entraînement Isolation Forest pour la détection d'anomalies
        self.isolation_forest.fit(X_scaled)
        
        # Préparation des données pour le prédicteur
        sequence_length = 10
        X_seq, y_seq = self.prepare_sequences(data, sequence_length)
        
        if len(X_seq) > 0:  # Vérifier qu'il y a assez de données
            # Entraînement du prédicteur
            self.predictor.fit(X_seq, y_seq)
            
            # Sauvegarde des modèles
            joblib.dump(self.scaler, os.path.join(self.model_path, "scaler.joblib"))
            joblib.dump(self.isolation_forest, os.path.join(self.model_path, "isolation_forest.joblib"))
            joblib.dump(self.predictor, os.path.join(self.model_path, "predictor.joblib"))
        else:
            raise ValueError("Pas assez de données pour l'entraînement")

    def predict_anomalies(self, data: pd.DataFrame, threshold: float = 1.5) -> Dict[str, Any]:
        """Détecte les anomalies dans les KPIs en utilisant plusieurs méthodes"""
        # Préparation des données
        numeric_features = data.select_dtypes(include=['float64', 'int64']).columns
        X = data[numeric_features]
        X_scaled = self.scaler.transform(X)
        
        # 1. Détection avec Isolation Forest
        isolation_scores = self.isolation_forest.predict(X_scaled)
        isolation_anomalies = np.where(isolation_scores == -1)[0]
        
        # 2. Détection avec Z-score
        zscore_anomalies = self.detect_anomalies_zscore(data)
        
        # 3. Détection avec moyenne mobile
        ma_anomalies = self.detect_anomalies_moving_average(data)
        
        # 4. Prédiction avec Random Forest
        rf_anomalies = []
        X_seq, _ = self.prepare_sequences(data)
        if len(X_seq) > 0:
            predictions = self.predictor.predict(X_seq)
            actual_values = X_scaled[10:]  # Skip first 10 points due to sequence length
            prediction_errors = np.mean(np.abs(predictions - actual_values), axis=1)
            rf_anomalies = np.where(prediction_errors > threshold)[0] + 10  # Ajuster les indices
        
        # Obtenir le consensus des anomalies
        methods_results = {
            "isolation_forest": list(isolation_anomalies),
            "zscore": zscore_anomalies,
            "moving_average": ma_anomalies,
            "random_forest": list(rf_anomalies)
        }
        
        consensus_anomalies = self.get_consensus_anomalies(data, methods_results)
        
        # Préparer les résultats détaillés
        anomalies = []
        for idx in consensus_anomalies:
            if idx < len(data):  # Vérifier que l'index est valide
                # Analyser les causes de l'anomalie
                cause_analysis = self.analyze_cause(data, idx)
                
                anomaly = {
                    "index": int(idx),
                    "timestamp": data.index[idx] if isinstance(data.index, pd.DatetimeIndex) else int(idx),
                    "values": data.iloc[idx][numeric_features].to_dict(),
                    "detection_methods": [
                        method for method, indices in methods_results.items()
                        if idx in indices
                    ],
                    "severity": "high" if len([
                        method for method, indices in methods_results.items()
                        if idx in indices
                    ]) >= 3 else "medium",
                    "causes": cause_analysis["causes"],
                    "primary_cause": cause_analysis["primary_cause"],
                    "cause_details": cause_analysis["details"],
                    "severity_score": cause_analysis["severity_score"]
                }
                anomalies.append(anomaly)
        
        return {
            "anomalies": anomalies,
            "methods_summary": {
                method: len(indices) for method, indices in methods_results.items()
            },
            "consensus_count": len(consensus_anomalies),
            "kpi_summary": {
                "total_severe_causes": sum(1 for a in anomalies if a["severity_score"] >= 6),
                "most_common_cause": max(
                    [cause for a in anomalies for cause in a["causes"]],
                    key=lambda x: sum(1 for a in anomalies if x in a["causes"]),
                    default="No causes found"
                )
            }
        }

    def load_models(self):
        """Charge les modèles sauvegardés"""
        try:
            self.scaler = joblib.load(os.path.join(self.model_path, "scaler.joblib"))
            self.isolation_forest = joblib.load(os.path.join(self.model_path, "isolation_forest.joblib"))
            self.predictor = joblib.load(os.path.join(self.model_path, "predictor.joblib"))
            return True
        except:
            return False 