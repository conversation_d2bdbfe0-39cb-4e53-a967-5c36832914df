{"_from": "internmap@^1.0.0", "_id": "internmap@1.0.1", "_inBundle": false, "_integrity": "sha512-lDB5YccMydFBtasVtxnZ3MRBHuaoE8GKsppq+EchKL2U4nK/DmEpPHNH8MZe5HkMtpSiTSOZwfN0tzYjO/lJEw==", "_location": "/d3-geo/internmap", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "internmap@^1.0.0", "name": "internmap", "escapedName": "internmap", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/d3-geo/d3-array"], "_resolved": "https://registry.npmjs.org/internmap/-/internmap-1.0.1.tgz", "_shasum": "0017cc8a3b99605f0302f2b198d272e015e5df95", "_spec": "internmap@^1.0.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\d3-geo\\node_modules\\d3-array", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/mbostock/internmap/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Map and Set with automatic key interning", "devDependencies": {"eslint": "^7.18.0", "rollup": "^2.37.1", "rollup-plugin-terser": "^7.0.2", "tape": "^4.13.3", "tape-await": "^0.1.2"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://github.com/mbostock/internmap/", "license": "ISC", "main": "dist/internmap.js", "module": "src/index.js", "name": "internmap", "repository": {"type": "git", "url": "git+https://github.com/mbostock/internmap.git"}, "scripts": {"postpublish": "git push && git push --tags", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape test/**/*-test.js && eslint src test"}, "sideEffects": false, "unpkg": "dist/internmap.min.js", "version": "1.0.1"}