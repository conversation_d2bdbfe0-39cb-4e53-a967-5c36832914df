{"_from": "@types/d3-selection@^2", "_id": "@types/d3-selection@2.0.5", "_inBundle": false, "_integrity": "sha512-71BorcY0yXl12S7lvb01JdaN9TpeUHBDb4RRhSq8U8BEkX/nIk5p7Byho+ZRTsx5nYLMpAbY3qt5EhqFzfGJlw==", "_location": "/@types/d3-selection", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/d3-selection@^2", "name": "@types/d3-selection", "escapedName": "@types%2fd3-selection", "scope": "@types", "rawSpec": "^2", "saveSpec": null, "fetchSpec": "^2"}, "_requiredBy": ["/@types/d3-zoom"], "_resolved": "https://registry.npmjs.org/@types/d3-selection/-/d3-selection-2.0.5.tgz", "_shasum": "360d92460947b272362d3be340e494f2e55c6171", "_spec": "@types/d3-selection@^2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\@types\\d3-zoom", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "denis<PERSON>", "url": "https://github.com/denisname"}, {"name": "<PERSON>", "url": "https://github.com/Methuselah96"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for d3-selection", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-selection", "license": "MIT", "main": "", "name": "@types/d3-selection", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-selection"}, "scripts": {}, "typeScriptVersion": "4.8", "types": "index.d.ts", "typesPublisherContentHash": "345f02ecc75769a336916aa985a35492580f66601c0060c8e36459251d156de2", "version": "2.0.5"}