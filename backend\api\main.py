"""
Module principal de l'API FastAPI pour l'analyse des données de roaming
"""
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
import os
import sys
import uvicorn
import traceback
import time
import logging
from .routers import kpi_routes, roaming, analysis, auth, files, reports, smart_report
from .services.ml_service import MLService
from .Kpi_service_clean import get_steering_success_by_country
from .network_type_endpoint import get_network_type_distribution
from .ml.init_ml import init_ml_environment

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import des routeurs
try:
    from .routers import analysis, kpi_routes, reports, files, roaming, auth, smart_report
except ImportError as e:
    logger.error(f"Erreur lors de l'importation des routeurs: {str(e)}")
    analysis = kpi_routes = reports = files = roaming = auth = smart_report = None

# Import des routes
try:
    from .routes import router as routes_router
    logger.info("Routes importées avec succès")
except ImportError as e:
    logger.error(f"Impossible d'importer les routes: {str(e)}")
    routes_router = None

# Configuration de l'application
app = FastAPI(
    title="API d'analyse de données de roaming",
    description="API pour l'analyse des données de roaming",
    version="1.0.0"
)

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import du middleware de sécurité
try:
    from .utils.security_headers import SecurityHeadersMiddleware
    # Ajout du middleware pour les headers de sécurité
    app.add_middleware(SecurityHeadersMiddleware)
except ImportError as e:
    logger.error(f"Impossible d'importer le middleware de sécurité: {str(e)}")

# Middleware pour logger les requêtes et leur temps d'exécution
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    logger.info(f"Requête entrante: {request.method} {request.url}")
    
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        logger.info(f"Requête traitée en {process_time:.3f} secondes")
        return response
    except Exception as e:
        logger.error(f"Erreur lors du traitement de la requête: {str(e)}")
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"detail": f"Erreur serveur: {str(e)}"}
        )

# Création des dossiers nécessaires
os.makedirs("uploads", exist_ok=True)
os.makedirs("reports", exist_ok=True)

# Montage des dossiers statiques
uploads_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "uploads")
if os.path.exists(uploads_dir):
    app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")
app.mount("/reports", StaticFiles(directory="reports"), name="reports")

# Initialisation du service ML
ml_service = MLService()

# Initialisation de l'environnement ML
if not init_ml_environment():
    logger.error("Erreur lors de l'initialisation de l'environnement ML")
    sys.exit(1)
logger.info("Environnement ML initialisé avec succès")

# Routes
app.include_router(kpi_routes.router, prefix="/api/kpi", tags=["KPI"])
app.include_router(roaming.router, prefix="/api/roaming", tags=["Roaming"])
app.include_router(analysis.router, prefix="/api/analysis", tags=["Analysis"])
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(files.router, prefix="/api/files", tags=["Files"])
app.include_router(reports.router, prefix="/api/reports", tags=["Reports"])
app.include_router(smart_report.router, prefix="/api/smart-report", tags=["Smart Report"])

# Inclusion du routeur des routes si disponible
if routes_router:
    logger.info("Ajout des routes API")
    app.include_router(routes_router, prefix="", tags=["API"])
else:
    logger.warning("Routes API non disponibles")

# Ajout du routeur des alertes
try:
    from .routers import alert_routes
    app.include_router(alert_routes.router, prefix="/api/kpi", tags=["Alerts"])
except ImportError as e:
    logger.warning(f"Impossible d'importer le routeur des alertes: {str(e)}")

@app.get("/")
def root():
    return {"message": "API d'analyse de données de roaming"}

@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """
    Page de documentation Swagger UI personnalisée avec support pour l'authentification
    """
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title="API Documentation",
        oauth2_redirect_url="/docs/oauth2-redirect",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        swagger_ui_parameters={
            "persistAuthorization": True,
            "withCredentials": True,
        }
    )

@app.get("/openapi.json", include_in_schema=False)
async def get_open_api_endpoint():
    """
    Génère le schéma OpenAPI avec des composants de sécurité personnalisés
    """
    openapi_schema = get_openapi(
        title="Roaming Data Analysis API",
        version="1.0.0",
        description="API pour l'analyse des données de roaming avec authentification Firebase",
        routes=app.routes,
    )
    
    # Ajout des composants de sécurité
    openapi_schema["components"] = {
        "securitySchemes": {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT"
            }
        }
    }
    
    # Applique l'authentification à toutes les routes
    openapi_schema["security"] = [{"BearerAuth": []}]
    
    return openapi_schema

@app.get("/api/health")
async def health_check():
    """Vérifie l'état de l'API"""
    return {"status": "healthy", "version": "1.0.0"}

@app.get("/api/network_performance_stats")
async def network_performance_stats_endpoint():
    """
    Récupère les statistiques de performance du réseau
    """
    try:
        from .Kpi_service_clean import get_network_performance_stats
        result = get_network_performance_stats()
        return result
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des statistiques de performance du réseau: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/weeks")
async def weeks_endpoint():
    """
    Récupère la liste des semaines disponibles dans les données
    """
    try:
        from .Kpi_service_clean import get_db_connection
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
        
        cursor = connection.cursor(dictionary=True)
        cursor.execute("""
            SELECT 
                DISTINCT WEEK(Timestamp) as week,
                COUNT(*) as count
            FROM steeringofroaming
            WHERE Timestamp IS NOT NULL
            GROUP BY WEEK(Timestamp)
            ORDER BY week
        """)
        results = cursor.fetchall()
        connection.close()
        
        return [{"week": row['week'], "count": row['count']} for row in results if row['week']]
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des semaines: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/countries")
async def countries_endpoint(country: str = None):
    """
    Récupère la liste des pays disponibles dans les données
    """
    try:
        from .Kpi_service_clean import get_db_connection
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
        
        cursor = connection.cursor(dictionary=True)
        query = """
            SELECT 
                a_location_country as value,
                COUNT(*) as count
            FROM steeringofroaming
            WHERE a_location_country IS NOT NULL 
            AND a_location_country != ''
            AND TCName = 'SteeringOfRoaming'
            GROUP BY a_location_country
            ORDER BY COUNT(*) DESC
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        connection.close()
        
        countries = [{"id": row['value'], "name": row['value'], "count": row['count']} for row in results if row['value']]
        
        logger.info(f"Pays récupérés: {len(countries)}")
        
        return {
            "success": True,
            "data": countries,
            "count": len(countries)
        }
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des pays: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "data": [],
            "message": str(e)
        }

@app.get("/api/operators")
async def operators_endpoint(country: str = None):
    """
    Récupère la liste des opérateurs disponibles dans les données
    """
    try:
        from .Kpi_service_clean import get_db_connection
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
        
        cursor = connection.cursor(dictionary=True)
        query = """
            SELECT 
                a_UsedPLMNName as value,
                COUNT(*) as count
            FROM steeringofroaming
            WHERE a_UsedPLMNName IS NOT NULL 
            AND a_UsedPLMNName != ''
            AND TCName = 'SteeringOfRoaming'
        """
        
        params = []
        if country and country != 'all':
            query += " AND a_location_country = %s"
            params.append(country)
            
        query += " GROUP BY a_UsedPLMNName ORDER BY COUNT(*) DESC"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        connection.close()
        
        operators = [{"id": row['value'], "name": row['value'], "count": row['count']} for row in results if row['value']]
        
        logger.info(f"Opérateurs récupérés: {len(operators)}")
        
        return {
            "success": True,
            "data": operators,
            "count": len(operators)
        }
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des opérateurs: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "data": [],
            "message": str(e)
        }

@app.get("/api/steering_chart_data")
async def steering_chart_data_endpoint(
    country: str = None,
    operator: str = None,
    start_date: str = None,
    end_date: str = None
):
    """
    Récupère les données pour le graphique de steering
    """
    try:
        from .Kpi_service_clean import get_steering_chart_data
        result = get_steering_chart_data(country, operator, start_date, end_date)
        return result
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des données pour le graphique de steering: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/steering_success_by_country_filtered")
async def steering_success_by_country_filtered_endpoint(
    country: str = None,
    operator: str = None,
    period: str = None,
    year: int = None,
    month: int = None,
    week: int = None,
    day: int = None,
    hour: int = None,
    verdict: str = None,
    start_date: str = None,
    end_date: str = None
):
    """
    Récupère les données de succès du steering par pays avec filtres
    """
    try:
        data = get_steering_success_by_country(
            country=country,
            operator=operator,
            period=period,
            year=year,
            month=month,
            week=week,
            day=day,
            hour=hour,
            verdict=verdict,
            start_date=start_date,
            end_date=end_date
        )
        return {
            "success": True,
            "data": data,
            "message": "Données récupérées avec succès"
        }
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des données de steering: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/network_type_distribution")
async def network_type_distribution_endpoint(country: str = None, operator: str = None):
    """
    Endpoint pour obtenir la distribution des types de réseau (3G/4G)
    """
    try:
        return await get_network_type_distribution(country, operator)
    except Exception as e:
        logger.error(f"Erreur lors de la récupération de la distribution des types de réseau: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def start():
    """Démarre le serveur avec Uvicorn"""
    uvicorn.run("api.main:app", host="0.0.0.0", port=8000, reload=True)

if __name__ == "__main__":
    start()