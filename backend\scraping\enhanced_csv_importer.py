#!/usr/bin/env python3
"""
Importeur CSV amélioré pour mapper toutes les colonnes des fichiers GlobalRoamer
"""

import os
import sys
import pandas as pd
import pymysql
import logging
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedCSVImporter:
    """Importeur CSV amélioré pour GlobalRoamer avec mapping complet des colonnes"""
    
    def __init__(self):
        """Initialiser l'importeur avec la configuration de base de données"""
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'database': 'kpi',
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        # Mapping des tables selon le nom du fichier
        self.table_mapping = {
            'steeringofroaming': 'steeringofroaming',
            'http_download_20': 'http_download_20'
        }
        
        # Colonnes communes pour SteeringOfRoaming (basées sur votre capture d'écran)
        self.steering_columns = [
            'TestcaseId', 'OrderId', 'TCName', 'Timestamp', 'Verdict',
            'TestDefinitionPath', 'User', 'UserGroup', 'errorId', 'errorText',
            'TCTestDefinitionId', 'errorSideCountry', 'errorSideId', 'errorSideLocation',
            'errorSideNumber', 'a_location_country', 'a_location_id', 'a_location_number',
            'a_operator_country', 'a_operator_id', 'a_operator_name', 'a_operator_number',
            'b_location_country', 'b_location_id', 'b_location_number',
            'b_operator_country', 'b_operator_id', 'b_operator_name', 'b_operator_number',
            'DayOfMonth', 'DayOfWeek', 'HourOfDay', 'MonthOfYear', 'WeekOfYear'
        ]
        
        # Colonnes pour HTTP_Download_20
        self.http_download_columns = [
            'TestcaseId', 'OrderId', 'TCName', 'Timestamp', 'Verdict',
            'TestDefinitionPath', 'User', 'UserGroup', 'errorId', 'errorText',
            'download_speed_mbps', 'file_size_mb', 'download_time_seconds',
            'connection_type', 'network_quality', 'signal_strength',
            'a_location_country', 'a_location_id', 'a_operator_name',
            'DayOfMonth', 'DayOfWeek', 'HourOfDay', 'MonthOfYear', 'WeekOfYear'
        ]
    
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        return pymysql.connect(**self.db_config)
    
    def detect_table_from_filename(self, filename: str) -> str:
        """Détecter la table de destination basée sur le nom du fichier"""
        filename_lower = filename.lower()
        
        if 'steeringofroaming' in filename_lower:
            return 'steeringofroaming'
        elif 'http_download' in filename_lower:
            return 'http_download_20'
        else:
            # Par défaut, utiliser SteeringOfRoaming
            logger.warning(f"⚠️ Type de fichier non reconnu: {filename}, utilisation de steeringofroaming par défaut")
            return 'steeringofroaming'
    
    def create_table_if_not_exists(self, table_name: str, columns: List[str]) -> bool:
        """Créer la table si elle n'existe pas avec toutes les colonnes nécessaires"""
        try:
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                # Vérifier si la table existe
                cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                table_exists = cursor.fetchone()
                
                if not table_exists:
                    logger.info(f"📋 Création de la table {table_name}")
                    
                    # Créer la structure de base
                    if table_name == 'steeringofroaming':
                        create_sql = self._get_steering_table_sql()
                    elif table_name == 'http_download_20':
                        create_sql = self._get_http_download_table_sql()
                    else:
                        create_sql = self._get_generic_table_sql(table_name, columns)
                    
                    cursor.execute(create_sql)
                    logger.info(f"✅ Table {table_name} créée")
                else:
                    logger.info(f"✅ Table {table_name} existe déjà")
                    
                    # Vérifier et ajouter les colonnes manquantes
                    self._add_missing_columns(cursor, table_name, columns)
                
                connection.commit()
            
            connection.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la création de la table {table_name}: {e}")
            return False
    
    def _get_steering_table_sql(self) -> str:
        """SQL pour créer la table steeringofroaming avec toutes les colonnes"""
        return """
        CREATE TABLE IF NOT EXISTS steeringofroaming (
            id INT AUTO_INCREMENT PRIMARY KEY,
            TestcaseId VARCHAR(255),
            OrderId VARCHAR(255),
            TCName VARCHAR(255),
            Timestamp DATETIME(3),
            Verdict VARCHAR(50),
            TestDefinitionPath TEXT,
            User VARCHAR(255),
            UserGroup VARCHAR(255),
            errorId VARCHAR(255),
            errorText TEXT,
            TCTestDefinitionId VARCHAR(255),
            errorSideCountry VARCHAR(255),
            errorSideId VARCHAR(255),
            errorSideLocation VARCHAR(255),
            errorSideNumber VARCHAR(255),
            a_location_country VARCHAR(255),
            a_location_id VARCHAR(255),
            a_location_number VARCHAR(255),
            a_operator_country VARCHAR(255),
            a_operator_id VARCHAR(255),
            a_operator_name VARCHAR(255),
            a_operator_number VARCHAR(255),
            b_location_country VARCHAR(255),
            b_location_id VARCHAR(255),
            b_location_number VARCHAR(255),
            b_operator_country VARCHAR(255),
            b_operator_id VARCHAR(255),
            b_operator_name VARCHAR(255),
            b_operator_number VARCHAR(255),
            DayOfMonth INT,
            DayOfWeek VARCHAR(20),
            HourOfDay INT,
            MonthOfYear INT,
            WeekOfYear INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_timestamp (Timestamp),
            INDEX idx_verdict (Verdict),
            INDEX idx_country (a_location_country),
            INDEX idx_tcname (TCName)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
    
    def _get_http_download_table_sql(self) -> str:
        """SQL pour créer la table http_download_20"""
        return """
        CREATE TABLE IF NOT EXISTS http_download_20 (
            id INT AUTO_INCREMENT PRIMARY KEY,
            TestcaseId VARCHAR(255),
            OrderId VARCHAR(255),
            TCName VARCHAR(255),
            Timestamp DATETIME(3),
            Verdict VARCHAR(50),
            TestDefinitionPath TEXT,
            User VARCHAR(255),
            UserGroup VARCHAR(255),
            errorId VARCHAR(255),
            errorText TEXT,
            download_speed_mbps DECIMAL(10,2),
            file_size_mb DECIMAL(10,2),
            download_time_seconds DECIMAL(10,2),
            connection_type VARCHAR(100),
            network_quality VARCHAR(100),
            signal_strength VARCHAR(100),
            a_location_country VARCHAR(255),
            a_location_id VARCHAR(255),
            a_operator_name VARCHAR(255),
            DayOfMonth INT,
            DayOfWeek VARCHAR(20),
            HourOfDay INT,
            MonthOfYear INT,
            WeekOfYear INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_timestamp (Timestamp),
            INDEX idx_verdict (Verdict),
            INDEX idx_country (a_location_country)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
    
    def _add_missing_columns(self, cursor, table_name: str, required_columns: List[str]):
        """Ajouter les colonnes manquantes à la table"""
        try:
            # Obtenir les colonnes existantes
            cursor.execute(f"DESCRIBE {table_name}")
            existing_columns = {row['Field'] for row in cursor.fetchall()}
            
            # Trouver les colonnes manquantes
            missing_columns = set(required_columns) - existing_columns
            
            if missing_columns:
                logger.info(f"📋 Ajout de {len(missing_columns)} colonne(s) manquante(s) à {table_name}")
                
                for column in missing_columns:
                    # Déterminer le type de colonne
                    column_type = self._get_column_type(column)
                    
                    alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {column} {column_type}"
                    cursor.execute(alter_sql)
                    logger.info(f"✅ Colonne ajoutée: {column} ({column_type})")
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'ajout des colonnes: {e}")
    
    def _get_column_type(self, column_name: str) -> str:
        """Déterminer le type de colonne basé sur le nom"""
        column_lower = column_name.lower()
        
        if 'timestamp' in column_lower or 'time' in column_lower:
            return 'DATETIME(3)'
        elif 'id' in column_lower and column_lower.endswith('id'):
            return 'VARCHAR(255)'
        elif any(word in column_lower for word in ['speed', 'size', 'mbps', 'mb', 'seconds']):
            return 'DECIMAL(10,2)'
        elif any(word in column_lower for word in ['day', 'hour', 'month', 'week', 'year']):
            return 'INT'
        elif 'text' in column_lower or 'path' in column_lower or 'error' in column_lower:
            return 'TEXT'
        else:
            return 'VARCHAR(255)'
    
    def import_csv_file(self, csv_file_path: str) -> bool:
        """Importer un fichier CSV avec mapping complet des colonnes"""
        try:
            logger.info(f"📥 Import amélioré du fichier: {csv_file_path}")
            
            # Détecter la table de destination
            filename = os.path.basename(csv_file_path)
            table_name = self.detect_table_from_filename(filename)
            logger.info(f"🎯 Table de destination: {table_name}")
            
            # Lire le fichier CSV
            df = pd.read_csv(csv_file_path, encoding='utf-8', low_memory=False)
            logger.info(f"📊 {len(df)} lignes lues depuis le CSV")
            logger.info(f"📋 {len(df.columns)} colonnes détectées: {list(df.columns)}")
            
            if len(df) == 0:
                logger.warning("⚠️ Fichier CSV vide")
                return True
            
            # Créer la table avec toutes les colonnes nécessaires
            all_columns = list(df.columns)
            if not self.create_table_if_not_exists(table_name, all_columns):
                return False
            
            # Préparer les données pour l'insertion
            df_clean = self._prepare_dataframe_for_insert(df, table_name)
            
            # Insérer les données par lots
            return self._insert_data_batch(df_clean, table_name)
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'import du fichier {csv_file_path}: {e}")
            return False
    
    def _prepare_dataframe_for_insert(self, df: pd.DataFrame, table_name: str) -> pd.DataFrame:
        """Préparer le DataFrame pour l'insertion"""
        df_clean = df.copy()

        # Remplacer les valeurs NaN par None de manière plus robuste
        df_clean = df_clean.fillna('')  # D'abord remplacer par des chaînes vides
        df_clean = df_clean.replace(['nan', 'NaN', 'NULL', 'null', ''], None)  # Puis par None
        
        # Ajouter les colonnes temporelles si elles n'existent pas
        if 'Timestamp' in df_clean.columns:
            df_clean['Timestamp'] = pd.to_datetime(df_clean['Timestamp'], errors='coerce')
            
            # Ajouter les colonnes dérivées du timestamp
            df_clean['DayOfMonth'] = df_clean['Timestamp'].dt.day
            df_clean['DayOfWeek'] = df_clean['Timestamp'].dt.strftime('(%w)%a')
            df_clean['HourOfDay'] = df_clean['Timestamp'].dt.hour
            df_clean['MonthOfYear'] = df_clean['Timestamp'].dt.month
            df_clean['WeekOfYear'] = df_clean['Timestamp'].dt.isocalendar().week
        
        return df_clean
    
    def _insert_data_batch(self, df: pd.DataFrame, table_name: str, batch_size: int = 1000) -> bool:
        """Insérer les données par lots"""
        try:
            connection = self.get_connection()
            
            # Préparer la requête d'insertion
            columns = list(df.columns)
            placeholders = ', '.join(['%s'] * len(columns))
            insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            # Insérer par lots
            total_inserted = 0
            
            with connection.cursor() as cursor:
                for start_idx in range(0, len(df), batch_size):
                    end_idx = min(start_idx + batch_size, len(df))
                    batch_df = df.iloc[start_idx:end_idx]
                    
                    # Convertir en liste de tuples
                    batch_data = [tuple(row) for row in batch_df.values]
                    
                    # Insérer le lot
                    cursor.executemany(insert_sql, batch_data)
                    total_inserted += len(batch_data)
                    
                    logger.info(f"📥 Lot inséré: {total_inserted}/{len(df)} lignes")
                
                connection.commit()
            
            connection.close()
            logger.info(f"✅ Import terminé: {total_inserted} lignes insérées dans {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'insertion: {e}")
            return False
    
    def import_multiple_files(self, csv_files: List[str]) -> Dict[str, bool]:
        """Importer plusieurs fichiers CSV"""
        results = {}
        
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                results[csv_file] = self.import_csv_file(csv_file)
            else:
                logger.warning(f"⚠️ Fichier non trouvé: {csv_file}")
                results[csv_file] = False
        
        return results

def main():
    """Test de l'importeur amélioré"""
    importer = EnhancedCSVImporter()
    
    # Tester avec un fichier existant
    test_file = Path(__file__).parent / "test_data" / "SteeringOfRoaming_20250706_test.csv"
    
    if test_file.exists():
        logger.info(f"🧪 Test avec le fichier: {test_file}")
        success = importer.import_csv_file(str(test_file))
        logger.info(f"Résultat: {'✅ Succès' if success else '❌ Échec'}")
    else:
        logger.warning("⚠️ Fichier de test non trouvé")

if __name__ == "__main__":
    main()
