{"_from": "d3-array@^2.5.0", "_id": "d3-array@2.12.1", "_inBundle": false, "_integrity": "sha512-B0ErZK/66mHtEsR1TkPEEkwdy+WDesimkM5gpZr5Dsg54BiTA5RXtYW5qTLIAcekaS9xfZrzBLF/OAkB3Qn1YQ==", "_location": "/d3-geo/d3-array", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-array@^2.5.0", "name": "d3-array", "escapedName": "d3-array", "rawSpec": "^2.5.0", "saveSpec": null, "fetchSpec": "^2.5.0"}, "_requiredBy": ["/d3-geo"], "_resolved": "https://registry.npmjs.org/d3-array/-/d3-array-2.12.1.tgz", "_shasum": "e20b41aafcdffdf5d50928004ececf815a465e81", "_spec": "d3-array@^2.5.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\d3-geo", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-array/issues"}, "bundleDependencies": false, "dependencies": {"internmap": "^1.0.0"}, "deprecated": false, "description": "Array manipulation, ordering, searching, summarizing, etc.", "devDependencies": {"@rollup/plugin-node-resolve": "11", "d3-random": "2", "eslint": "7", "jsdom": "16", "rollup": "2", "rollup-plugin-terser": "7", "tape": "4", "tape-await": "0.1"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-array/", "keywords": ["d3", "d3-module", "histogram", "bisect", "shuffle", "statistics", "search", "sort", "array"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-array.js", "module": "src/index.js", "name": "d3-array", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-array.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "./test/run.sh"}, "sideEffects": false, "unpkg": "dist/d3-array.min.js", "version": "2.12.1"}