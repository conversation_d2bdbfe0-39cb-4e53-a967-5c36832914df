// https://d3js.org/d3-transition/ v2.0.0 Copyright 2020 Mike <PERSON>
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports,require("d3-selection"),require("d3-dispatch"),require("d3-timer"),require("d3-interpolate"),require("d3-color"),require("d3-ease")):"function"==typeof define&&define.amd?define(["exports","d3-selection","d3-dispatch","d3-timer","d3-interpolate","d3-color","d3-ease"],n):n((t=t||self).d3=t.d3||{},t.d3,t.d3,t.d3,t.d3,t.d3,t.d3)}(this,function(t,n,e,r,i,o,u){"use strict";var a=e.dispatch("start","end","cancel","interrupt"),s=[],l=0,f=1,c=2,h=3,d=4,p=5,_=6;function v(t,n,e,i,o,u){var v=t.__transition;if(v){if(e in v)return}else t.__transition={};!function(t,n,e){var i,o=t.__transition;function u(l){var p,v,y,m;if(e.state!==f)return s();for(p in o)if((m=o[p]).name===e.name){if(m.state===h)return r.timeout(u);m.state===d?(m.state=_,m.timer.stop(),m.on.call("interrupt",t,t.__data__,m.index,m.group),delete o[p]):+p<n&&(m.state=_,m.timer.stop(),m.on.call("cancel",t,t.__data__,m.index,m.group),delete o[p])}if(r.timeout(function(){e.state===h&&(e.state=d,e.timer.restart(a,e.delay,e.time),a(l))}),e.state=c,e.on.call("start",t,t.__data__,e.index,e.group),e.state===c){for(e.state=h,i=new Array(y=e.tween.length),p=0,v=-1;p<y;++p)(m=e.tween[p].value.call(t,t.__data__,e.index,e.group))&&(i[++v]=m);i.length=v+1}}function a(n){for(var r=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(s),e.state=p,1),o=-1,u=i.length;++o<u;)i[o].call(t,r);e.state===p&&(e.on.call("end",t,t.__data__,e.index,e.group),s())}function s(){for(var r in e.state=_,e.timer.stop(),delete o[n],o)return;delete t.__transition}o[n]=e,e.timer=r.timer(function(t){e.state=f,e.timer.restart(u,e.delay,e.time),e.delay<=t&&u(t-e.delay)},0,e.time)}(t,e,{name:n,index:i,group:o,on:a,tween:s,time:u.time,delay:u.delay,duration:u.duration,ease:u.ease,timer:null,state:l})}function y(t,n){var e=w(t,n);if(e.state>l)throw new Error("too late; already scheduled");return e}function m(t,n){var e=w(t,n);if(e.state>h)throw new Error("too late; already running");return e}function w(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}function g(t,n){var e,r,i,o=t.__transition,u=!0;if(o){for(i in n=null==n?null:n+"",o)(e=o[i]).name===n?(r=e.state>c&&e.state<p,e.state=_,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]):u=!1;u&&delete t.__transition}}function b(t,n,e){var r=t._id;return t.each(function(){var t=m(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return w(t,r).value[n]}}function x(t,n){var e;return("number"==typeof n?i.interpolateNumber:n instanceof o.color?i.interpolateRgb:(e=o.color(n))?(n=e,i.interpolateRgb):i.interpolateString)(t,n)}var A=n.selection.prototype.constructor;function E(t){return function(){this.style.removeProperty(t)}}var S=0;function T(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function N(t){return n.selection().transition(t)}function q(){return++S}var C=n.selection.prototype;T.prototype=N.prototype={constructor:T,select:function(t){var e=this._name,r=this._id;"function"!=typeof t&&(t=n.selector(t));for(var i=this._groups,o=i.length,u=new Array(o),a=0;a<o;++a)for(var s,l,f=i[a],c=f.length,h=u[a]=new Array(c),d=0;d<c;++d)(s=f[d])&&(l=t.call(s,s.__data__,d,f))&&("__data__"in s&&(l.__data__=s.__data__),h[d]=l,v(h[d],e,r,d,h,w(s,r)));return new T(u,this._parents,e,r)},selectAll:function(t){var e=this._name,r=this._id;"function"!=typeof t&&(t=n.selectorAll(t));for(var i=this._groups,o=i.length,u=[],a=[],s=0;s<o;++s)for(var l,f=i[s],c=f.length,h=0;h<c;++h)if(l=f[h]){for(var d,p=t.call(l,l.__data__,h,f),_=w(l,r),y=0,m=p.length;y<m;++y)(d=p[y])&&v(d,e,r,y,p,_);u.push(p),a.push(l)}return new T(u,a,e,r)},filter:function(t){"function"!=typeof t&&(t=n.matcher(t));for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o)for(var u,a=e[o],s=a.length,l=i[o]=[],f=0;f<s;++f)(u=a[f])&&t.call(u,u.__data__,f,a)&&l.push(u);return new T(i,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),a=0;a<o;++a)for(var s,l=n[a],f=e[a],c=l.length,h=u[a]=new Array(c),d=0;d<c;++d)(s=l[d]||f[d])&&(h[d]=s);for(;a<r;++a)u[a]=n[a];return new T(u,this._parents,this._name,this._id)},selection:function(){return new A(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=q(),r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],s=a.length,l=0;l<s;++l)if(u=a[l]){var f=w(u,n);v(u,t,e,l,a,{time:f.time+f.delay+f.duration,delay:0,duration:f.duration,ease:f.ease})}return new T(r,this._parents,t,e)},call:C.call,nodes:C.nodes,node:C.node,size:C.size,empty:C.empty,each:C.each,on:function(t,n){var e=this._id;return arguments.length<2?w(this.node(),e).on.on(t):this.each(function(t,n,e){var r,i,o=function(t){return(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})}(n)?y:m;return function(){var u=o(this,t),a=u.on;a!==r&&(i=(r=a).copy()).on(n,e),u.on=i}}(e,t,n))},attr:function(t,e){var r=n.namespace(t),o="transform"===r?i.interpolateTransformSvg:x;return this.attrTween(t,"function"==typeof e?(r.local?function(t,n,e){var r,i,o;return function(){var u,a,s=e(this);if(null!=s)return(u=this.getAttributeNS(t.space,t.local))===(a=s+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,s));this.removeAttributeNS(t.space,t.local)}}:function(t,n,e){var r,i,o;return function(){var u,a,s=e(this);if(null!=s)return(u=this.getAttribute(t))===(a=s+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,s));this.removeAttribute(t)}})(r,o,b(this,"attr."+t,e)):null==e?(r.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(r):(r.local?function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttributeNS(t.space,t.local);return u===o?null:u===r?i:i=n(r=u,e)}}:function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttribute(t);return u===o?null:u===r?i:i=n(r=u,e)}})(r,o,e))},attrTween:function(t,e){var r="attr."+t;if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==e)return this.tween(r,null);if("function"!=typeof e)throw new Error;var i=n.namespace(t);return this.tween(r,(i.local?function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttributeNS(t.space,t.local,n.call(this,e))}}(t,i)),e}return i._value=n,i}:function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttribute(t,n.call(this,e))}}(t,i)),e}return i._value=n,i})(i,e))},style:function(t,e,r){var o="transform"==(t+="")?i.interpolateTransformCss:x;return null==e?this.styleTween(t,function(t,e){var r,i,o;return function(){var u=n.style(this,t),a=(this.style.removeProperty(t),n.style(this,t));return u===a?null:u===r&&a===i?o:o=e(r=u,i=a)}}(t,o)).on("end.style."+t,E(t)):"function"==typeof e?this.styleTween(t,function(t,e,r){var i,o,u;return function(){var a=n.style(this,t),s=r(this),l=s+"";return null==s&&(this.style.removeProperty(t),l=s=n.style(this,t)),a===l?null:a===i&&l===o?u:(o=l,u=e(i=a,s))}}(t,o,b(this,"style."+t,e))).each(function(t,n){var e,r,i,o,u="style."+n,a="end."+u;return function(){var s=m(this,t),l=s.on,f=null==s.value[u]?o||(o=E(n)):void 0;l===e&&i===f||(r=(e=l).copy()).on(a,i=f),s.on=r}}(this._id,t)):this.styleTween(t,function(t,e,r){var i,o,u=r+"";return function(){var a=n.style(this,t);return a===u?null:a===i?o:o=e(i=a,r)}}(t,o,e),r).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw new Error;return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(t,n,e){return function(r){this.style.setProperty(t,n.call(this,r),e)}}(t,o,e)),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var n=t(this);this.textContent=null==n?"":n}}(b(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){return function(n){this.textContent=t.call(this,n)}}(r)),n}return r._value=t,r}(t))},remove:function(){return this.on("end.remove",function(t){return function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}}(this._id))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=w(this.node(),e).tween,o=0,u=i.length;o<u;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=m(this,t),o=i.tween;if(o!==e)for(var u=0,a=(r=e=o).length;u<a;++u)if(r[u].name===n){(r=r.slice()).splice(u,1);break}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw new Error;return function(){var o=m(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},s=0,l=i.length;s<l;++s)if(i[s].name===n){i[s]=a;break}s===l&&i.push(a)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){y(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){y(this,t).delay=n}})(n,t)):w(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){m(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){m(this,t).duration=n}})(n,t)):w(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw new Error;return function(){m(this,t).ease=n}}(n,t)):w(this.node(),n).ease},easeVarying:function(t){if("function"!=typeof t)throw new Error;return this.each(function(t,n){return function(){var e=n.apply(this,arguments);if("function"!=typeof e)throw new Error;m(this,t).ease=e}}(this._id,t))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,u){var a={value:u},s={value:function(){0==--i&&o()}};e.each(function(){var e=m(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(a),n._.interrupt.push(a),n._.end.push(s)),e.on=n}),0===i&&o()})},[Symbol.iterator]:C[Symbol.iterator]};var P={time:null,delay:0,duration:250,ease:u.easeCubicInOut};function z(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw new Error(`transition ${n} not found`);return e}n.selection.prototype.interrupt=function(t){return this.each(function(){g(this,t)})},n.selection.prototype.transition=function(t){var n,e;t instanceof T?(n=t._id,t=t._name):(n=q(),(e=P).time=r.now(),t=null==t?null:t+"");for(var i=this._groups,o=i.length,u=0;u<o;++u)for(var a,s=i[u],l=s.length,f=0;f<l;++f)(a=s[f])&&v(a,t,n,f,s,e||z(a,n));return new T(i,this._parents,t,n)};var O=[null];t.active=function(t,n){var e,r,i=t.__transition;if(i)for(r in n=null==n?null:n+"",i)if((e=i[r]).state>f&&e.name===n)return new T([[t]],O,n,+r);return null},t.interrupt=g,t.transition=N,Object.defineProperty(t,"__esModule",{value:!0})});
