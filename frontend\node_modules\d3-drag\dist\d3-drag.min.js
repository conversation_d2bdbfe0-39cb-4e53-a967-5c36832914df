// https://d3js.org/d3-drag/ v2.0.0 Copyright 2020 Mike <PERSON>
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("d3-dispatch"),require("d3-selection")):"function"==typeof define&&define.amd?define(["exports","d3-dispatch","d3-selection"],t):t((e=e||self).d3=e.d3||{},e.d3,e.d3)}(this,function(e,t,n){"use strict";function o(e){e.stopImmediatePropagation()}function r(e){e.preventDefault(),e.stopImmediatePropagation()}function i(e){var t=e.document.documentElement,o=n.select(e).on("dragstart.drag",r,!0);"onselectstart"in t?o.on("selectstart.drag",r,!0):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function a(e,t){var o=e.document.documentElement,i=n.select(e).on("dragstart.drag",null);t&&(i.on("click.drag",r,!0),setTimeout(function(){i.on("click.drag",null)},0)),"onselectstart"in o?i.on("selectstart.drag",null):(o.style.MozUserSelect=o.__noselect,delete o.__noselect)}var u=e=>()=>e;function c(e,{sourceEvent:t,subject:n,target:o,identifier:r,active:i,x:a,y:u,dx:c,dy:l,dispatch:s}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:a,enumerable:!0,configurable:!0},y:{value:u,enumerable:!0,configurable:!0},dx:{value:c,enumerable:!0,configurable:!0},dy:{value:l,enumerable:!0,configurable:!0},_:{value:s}})}function l(e){return!e.ctrlKey&&!e.button}function s(){return this.parentNode}function d(e,t){return null==t?{x:e.x,y:e.y}:t}function f(){return navigator.maxTouchPoints||"ontouchstart"in this}c.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e},e.drag=function(){var e,g,h,v,m=l,p=s,b=d,y=f,x={},_=t.dispatch("start","drag","end"),w=0,j=0;function E(e){e.on("mousedown.drag",T).filter(y).on("touchstart.drag",P).on("touchmove.drag",q).on("touchend.drag touchcancel.drag",z).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function T(t,r){if(!v&&m.call(this,t,r)){var a=D(this,p.call(this,t,r),t,r,"mouse");a&&(n.select(t.view).on("mousemove.drag",k,!0).on("mouseup.drag",M,!0),i(t.view),o(t),h=!1,e=t.clientX,g=t.clientY,a("start",t))}}function k(t){if(r(t),!h){var n=t.clientX-e,o=t.clientY-g;h=n*n+o*o>j}x.mouse("drag",t)}function M(e){n.select(e.view).on("mousemove.drag mouseup.drag",null),a(e.view,h),r(e),x.mouse("end",e)}function P(e,t){if(m.call(this,e,t)){var n,r,i=e.changedTouches,a=p.call(this,e,t),u=i.length;for(n=0;n<u;++n)(r=D(this,a,e,t,i[n].identifier,i[n]))&&(o(e),r("start",e,i[n]))}}function q(e){var t,n,o=e.changedTouches,i=o.length;for(t=0;t<i;++t)(n=x[o[t].identifier])&&(r(e),n("drag",e,o[t]))}function z(e){var t,n,r=e.changedTouches,i=r.length;for(v&&clearTimeout(v),v=setTimeout(function(){v=null},500),t=0;t<i;++t)(n=x[r[t].identifier])&&(o(e),n("end",e,r[t]))}function D(e,t,o,r,i,a){var u,l,s,d=_.copy(),f=n.pointer(a||o,t);if(null!=(s=b.call(e,new c("beforestart",{sourceEvent:o,target:E,identifier:i,active:w,x:f[0],y:f[1],dx:0,dy:0,dispatch:d}),r)))return u=s.x-f[0]||0,l=s.y-f[1]||0,function o(a,g,h){var v,m=f;switch(a){case"start":x[i]=o,v=w++;break;case"end":delete x[i],--w;case"drag":f=n.pointer(h||g,t),v=w}d.call(a,e,new c(a,{sourceEvent:g,subject:s,target:E,identifier:i,active:v,x:f[0]+u,y:f[1]+l,dx:f[0]-m[0],dy:f[1]-m[1],dispatch:d}),r)}}return E.filter=function(e){return arguments.length?(m="function"==typeof e?e:u(!!e),E):m},E.container=function(e){return arguments.length?(p="function"==typeof e?e:u(e),E):p},E.subject=function(e){return arguments.length?(b="function"==typeof e?e:u(e),E):b},E.touchable=function(e){return arguments.length?(y="function"==typeof e?e:u(!!e),E):y},E.on=function(){var e=_.on.apply(_,arguments);return e===_?E:e},E.clickDistance=function(e){return arguments.length?(j=(e=+e)*e,E):Math.sqrt(j)},E},e.dragDisable=i,e.dragEnable=a,Object.defineProperty(e,"__esModule",{value:!0})});
