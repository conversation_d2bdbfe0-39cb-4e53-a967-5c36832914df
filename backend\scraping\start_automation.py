#!/usr/bin/env python3
"""
Script pour démarrer l'automatisation du scraping 24h/24
"""

import os
import sys
import time
import schedule
import logging
from datetime import datetime
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from automation_service import AutomationService

# Configuration du logging
log_file = Path(__file__).parent / "automation_24h.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class AutomationScheduler:
    """Planificateur d'automatisation 24h/24"""
    
    def __init__(self):
        self.automation_service = AutomationService()
        self.is_running = False
        
    def daily_scraping_job(self):
        """Tâche quotidienne de scraping (programmée à 2h du matin)"""
        logger.info("🕐 EXÉCUTION PROGRAMMÉE QUOTIDIENNE - 2H DU MATIN")
        logger.info("=" * 60)
        
        try:
            # Exécuter le scraping et import
            success = self.automation_service.run_scraping_and_import(days_back=1)
            
            if success:
                logger.info("✅ Scraping quotidien réussi")
                
                # Afficher les statistiques
                stats = self.automation_service.get_stats()
                logger.info(f"📊 Statistiques mises à jour:")
                logger.info(f"   - Total runs: {stats.get('total_runs', 0)}")
                logger.info(f"   - Successful: {stats.get('successful_runs', 0)}")
                logger.info(f"   - Failed: {stats.get('failed_runs', 0)}")
                logger.info(f"   - Files processed: {stats.get('files_processed', 0)}")
                
            else:
                logger.error("❌ Scraping quotidien échoué")
                
        except Exception as e:
            logger.error(f"💥 Erreur lors du scraping quotidien: {e}")
    
    def health_check_job(self):
        """Vérification de santé du système (toutes les 6h)"""
        logger.info("🔍 VÉRIFICATION DE SANTÉ DU SYSTÈME")
        logger.info("-" * 40)
        
        try:
            from csv_importer import CSVImporter
            
            importer = CSVImporter()
            connection = importer.get_connection()
            
            with connection.cursor() as cursor:
                # Compter les enregistrements totaux
                cursor.execute("SELECT COUNT(*) as total FROM steeringofroaming")
                total = cursor.fetchone()['total']
                
                # Compter les enregistrements récents
                cursor.execute("""
                    SELECT COUNT(*) as recent 
                    FROM steeringofroaming 
                    WHERE Timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                """)
                recent = cursor.fetchone()['recent']
                
                logger.info(f"📊 Total records: {total:,}")
                logger.info(f"🕐 Records dernières 24h: {recent:,}")
                
                # Alertes si nécessaire
                if recent == 0:
                    logger.warning("⚠️ ALERTE: Aucun nouvel enregistrement dans les dernières 24h")
                elif recent < 100:
                    logger.warning(f"⚠️ ALERTE: Peu d'enregistrements récents ({recent})")
                else:
                    logger.info("✅ Système en bonne santé")
            
            connection.close()
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la vérification de santé: {e}")
    
    def stats_report_job(self):
        """Rapport de statistiques (quotidien à 8h)"""
        logger.info("📊 RAPPORT QUOTIDIEN DE STATISTIQUES")
        logger.info("-" * 40)
        
        try:
            stats = self.automation_service.get_stats()
            
            logger.info(f"📈 Rapport du {datetime.now().strftime('%Y-%m-%d')}:")
            logger.info(f"   - Total d'exécutions: {stats.get('total_runs', 0)}")
            logger.info(f"   - Taux de réussite: {self._calculate_success_rate(stats):.1f}%")
            logger.info(f"   - Fichiers traités: {stats.get('files_processed', 0)}")
            logger.info(f"   - Dernier succès: {stats.get('last_success', 'Jamais')}")
            
            # Vérifier les tendances
            if stats.get('failed_runs', 0) > stats.get('successful_runs', 0):
                logger.warning("⚠️ ALERTE: Plus d'échecs que de succès récemment")
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du rapport de statistiques: {e}")
    
    def _calculate_success_rate(self, stats):
        """Calculer le taux de réussite"""
        total = stats.get('total_runs', 0)
        success = stats.get('successful_runs', 0)
        return (success / total * 100) if total > 0 else 0
    
    def setup_schedule(self):
        """Configurer la planification"""
        logger.info("⏰ CONFIGURATION DE LA PLANIFICATION")
        logger.info("=" * 50)
        
        # Scraping quotidien à 2h du matin
        schedule.every().day.at("02:00").do(self.daily_scraping_job)
        logger.info("✅ Scraping quotidien programmé à 2h00")
        
        # Vérification de santé toutes les 6h
        schedule.every(6).hours.do(self.health_check_job)
        logger.info("✅ Vérification de santé toutes les 6h")
        
        # Rapport quotidien à 8h
        schedule.every().day.at("08:00").do(self.stats_report_job)
        logger.info("✅ Rapport quotidien programmé à 8h00")
        
        # Vérification immédiate au démarrage
        logger.info("🔍 Vérification immédiate au démarrage...")
        self.health_check_job()
    
    def start(self):
        """Démarrer l'automatisation"""
        logger.info("🚀 DÉMARRAGE DE L'AUTOMATISATION 24H/24")
        logger.info("=" * 60)
        logger.info(f"📅 Date/Heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.setup_schedule()
        self.is_running = True
        
        logger.info("🔄 Boucle d'automatisation démarrée...")
        logger.info("💡 Appuyez sur Ctrl+C pour arrêter")
        
        try:
            while self.is_running:
                schedule.run_pending()
                time.sleep(60)  # Vérifier toutes les minutes
                
        except KeyboardInterrupt:
            logger.info("\n⏹️ Arrêt demandé par l'utilisateur")
            self.stop()
        except Exception as e:
            logger.error(f"💥 Erreur fatale: {e}")
            self.stop()
    
    def stop(self):
        """Arrêter l'automatisation"""
        logger.info("🛑 ARRÊT DE L'AUTOMATISATION")
        self.is_running = False
        schedule.clear()
        logger.info("✅ Automatisation arrêtée proprement")

def main():
    """Fonction principale"""
    print("🤖 AUTOMATISATION SCRAPING GLOBALROAMER 24H/24")
    print("=" * 60)
    print(f"📅 Démarrage: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📄 Logs: {Path(__file__).parent / 'automation_24h.log'}")
    print("=" * 60)
    
    try:
        scheduler = AutomationScheduler()
        scheduler.start()
        
    except Exception as e:
        logger.error(f"💥 Erreur fatale lors du démarrage: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
