#!/usr/bin/env python3
"""
Test de simulation du scraping automatique sur 24h
"""

import os
import sys
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from automation_service import AutomationService

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def simulate_24h_automation():
    """Simuler l'automatisation sur 24h avec des intervalles accélérés"""
    logger.info("🕐 SIMULATION DU SCRAPING AUTOMATIQUE 24H")
    logger.info("=" * 60)
    
    try:
        # Créer le service d'automatisation
        automation = AutomationService()
        
        # Simuler 24 heures avec des intervalles de 1 minute = 1 heure
        simulation_hours = 24
        interval_seconds = 60  # 1 minute = 1 heure simulée
        
        logger.info(f"🚀 Démarrage de la simulation")
        logger.info(f"⏰ {simulation_hours} heures simulées en {simulation_hours} minutes")
        logger.info(f"📊 Intervalle: {interval_seconds} secondes = 1 heure simulée")
        
        start_time = datetime.now()
        
        for hour in range(simulation_hours):
            current_sim_time = start_time + timedelta(hours=hour)
            logger.info(f"\n⏰ HEURE SIMULÉE: {hour:02d}:00 ({current_sim_time.strftime('%Y-%m-%d %H:%M')})")
            
            # Simuler l'exécution à 2h du matin (heure programmée)
            if hour == 2:
                logger.info("🎯 EXÉCUTION PROGRAMMÉE À 2H DU MATIN")
                logger.info("-" * 40)
                
                try:
                    # Exécuter le scraping et import
                    success = automation.run_scraping_and_import(days_back=1)
                    
                    if success:
                        logger.info("✅ Scraping et import réussis")
                    else:
                        logger.warning("⚠️ Scraping et import échoués")
                        
                    # Afficher les statistiques
                    stats = automation.get_stats()
                    logger.info(f"📊 Statistiques mises à jour:")
                    logger.info(f"   - Total runs: {stats.get('total_runs', 0)}")
                    logger.info(f"   - Successful: {stats.get('successful_runs', 0)}")
                    logger.info(f"   - Failed: {stats.get('failed_runs', 0)}")
                    
                except Exception as e:
                    logger.error(f"❌ Erreur lors de l'exécution programmée: {e}")
            
            # Simuler d'autres activités de monitoring
            elif hour % 6 == 0:  # Toutes les 6 heures
                logger.info("📊 Vérification de santé du système")
                
                # Vérifier la base de données
                try:
                    from csv_importer import CSVImporter
                    importer = CSVImporter()
                    connection = importer.get_connection()
                    
                    with connection.cursor() as cursor:
                        cursor.execute("SELECT COUNT(*) as count FROM steeringofroaming")
                        count = cursor.fetchone()['count']
                        logger.info(f"   📈 Enregistrements en base: {count}")
                        
                        # Vérifier les derniers enregistrements
                        cursor.execute("""
                            SELECT COUNT(*) as recent_count 
                            FROM steeringofroaming 
                            WHERE Timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                        """)
                        recent = cursor.fetchone()['recent_count']
                        logger.info(f"   🕐 Enregistrements dernières 24h: {recent}")
                        
                except Exception as e:
                    logger.warning(f"   ⚠️ Erreur de vérification DB: {e}")
            
            else:
                logger.info("💤 Système en veille...")
            
            # Attendre l'intervalle (sauf pour la dernière itération)
            if hour < simulation_hours - 1:
                time.sleep(interval_seconds)
        
        # Résumé final
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 SIMULATION TERMINÉE")
        logger.info("=" * 60)
        logger.info(f"⏱️ Durée réelle: {duration}")
        logger.info(f"🕐 Période simulée: 24 heures")
        
        # Statistiques finales
        try:
            final_stats = automation.get_stats()
            logger.info(f"\n📊 STATISTIQUES FINALES:")
            logger.info(f"   - Total d'exécutions: {final_stats.get('total_runs', 0)}")
            logger.info(f"   - Succès: {final_stats.get('successful_runs', 0)}")
            logger.info(f"   - Échecs: {final_stats.get('failed_runs', 0)}")
            logger.info(f"   - Fichiers traités: {final_stats.get('files_processed', 0)}")
            logger.info(f"   - Dernier succès: {final_stats.get('last_success', 'Jamais')}")
            logger.info(f"   - Dernier échec: {final_stats.get('last_failure', 'Jamais')}")
            
            # Calculer le taux de réussite
            total = final_stats.get('total_runs', 0)
            success = final_stats.get('successful_runs', 0)
            if total > 0:
                success_rate = (success / total) * 100
                logger.info(f"   - Taux de réussite: {success_rate:.1f}%")
                
                if success_rate >= 80:
                    logger.info("🎉 Système très fiable!")
                elif success_rate >= 60:
                    logger.warning("⚠️ Système moyennement fiable")
                else:
                    logger.error("❌ Système peu fiable - Corrections nécessaires")
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la récupération des statistiques finales: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale lors de la simulation: {e}")
        return False

def test_real_time_monitoring():
    """Tester le monitoring en temps réel"""
    logger.info("\n📡 TEST DE MONITORING EN TEMPS RÉEL")
    logger.info("=" * 50)
    
    try:
        automation = AutomationService()
        
        # Simuler 5 minutes de monitoring
        for minute in range(5):
            logger.info(f"⏰ Minute {minute + 1}/5")
            
            # Vérifier l'état du système
            stats = automation.get_stats()
            logger.info(f"   📊 Runs totaux: {stats.get('total_runs', 0)}")
            
            # Simuler une vérification de santé
            if minute % 2 == 0:
                logger.info("   🔍 Vérification de santé...")
                # Ici on pourrait ajouter des vérifications réelles
                logger.info("   ✅ Système opérationnel")
            
            time.sleep(10)  # 10 secondes par "minute"
        
        logger.info("✅ Monitoring en temps réel fonctionnel")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur de monitoring: {e}")
        return False

def main():
    """Fonction principale"""
    logger.info("🚀 DÉMARRAGE DU TEST D'AUTOMATISATION 24H")
    logger.info("=" * 70)
    logger.info(f"📅 Date/Heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Test de simulation 24h
        logger.info("\n1️⃣ PHASE 1: SIMULATION 24H ACCÉLÉRÉE")
        sim_success = simulate_24h_automation()
        
        # Test de monitoring temps réel
        logger.info("\n2️⃣ PHASE 2: MONITORING TEMPS RÉEL")
        monitor_success = test_real_time_monitoring()
        
        # Résumé global
        logger.info(f"\n🎯 RÉSUMÉ GLOBAL")
        logger.info("=" * 50)
        logger.info(f"Simulation 24h: {'✅ PASS' if sim_success else '❌ FAIL'}")
        logger.info(f"Monitoring temps réel: {'✅ PASS' if monitor_success else '❌ FAIL'}")
        
        if sim_success and monitor_success:
            logger.info("🎉 SYSTÈME D'AUTOMATISATION ENTIÈREMENT FONCTIONNEL!")
        elif sim_success or monitor_success:
            logger.warning("⚠️ Système partiellement fonctionnel")
        else:
            logger.error("❌ Système défaillant")
        
        return sim_success and monitor_success
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
