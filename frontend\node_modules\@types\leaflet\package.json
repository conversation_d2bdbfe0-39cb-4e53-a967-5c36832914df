{"_from": "@types/leaflet@^1.9.19", "_id": "@types/leaflet@1.9.19", "_inBundle": false, "_integrity": "sha512-pB+n2daHcZPF2FDaWa+6B0a0mSDf4dPU35y5iTXsx7x/PzzshiX5atYiS1jlBn43X7XvM8AP+AB26lnSk0J4GA==", "_location": "/@types/leaflet", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/leaflet@^1.9.19", "name": "@types/leaflet", "escapedName": "@types%2fleaflet", "scope": "@types", "rawSpec": "^1.9.19", "saveSpec": null, "fetchSpec": "^1.9.19"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/@types/leaflet/-/leaflet-1.9.19.tgz", "_shasum": "432d3c52c52abb3521e5dfcb3e6cfe9653807a6e", "_spec": "@types/leaflet@^1.9.19", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/alejo90"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/atd-schubert"}, {"name": "<PERSON>", "url": "https://github.com/mcauer"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ronikar"}, {"name": "<PERSON>", "url": "https://github.com/life777"}, {"name": "<PERSON>", "url": "https://github.com/henry<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/captain-igloo"}, {"name": "<PERSON>", "url": "https://github.com/someonewithpc"}], "dependencies": {"@types/geojson": "*"}, "deprecated": false, "description": "TypeScript definitions for leaflet", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/leaflet", "license": "MIT", "main": "", "name": "@types/leaflet", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/leaflet"}, "scripts": {}, "typeScriptVersion": "5.1", "types": "index.d.ts", "typesPublisherContentHash": "4308f7c4af017fa08b883b9e6b03ece4889e71bd35d45c32a2fff2a8c26e217c", "version": "1.9.19"}