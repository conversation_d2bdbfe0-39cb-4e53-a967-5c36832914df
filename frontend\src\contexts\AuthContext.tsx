import { User } from 'firebase/auth';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { firebaseAuthService } from '../services/firebaseAuth';

// Authentication status type
type AuthStatus = 'loading' | 'authenticated' | 'unauthenticated';

// User interface
interface AuthUser {
  id: string;
  name: string;
  email?: string;
  role?: string;
  firebaseUser?: User;
}

// Auth context interface
interface AuthContextType {
  status: AuthStatus;
  token: string | null;
  user: AuthUser | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

// Create context with default values
const AuthContext = createContext<AuthContextType>({
  status: 'loading',
  token: null,
  user: null,
  login: async () => {},
  logout: async () => {},
});

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [status, setStatus] = useState<AuthStatus>('loading');
  const [user, setUser] = useState<AuthUser | null>(null);
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const unsubscribe = firebaseAuthService.onAuthStateChanged(async (firebaseUser) => {
      try {
        if (firebaseUser) {
          const token = await firebaseUser.getIdToken();
          localStorage.setItem('firebase_token', token);
          setToken(token);
          setUser({
            id: firebaseUser.uid,
            name: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
            email: firebaseUser.email || undefined,
            role: 'user',
            firebaseUser
          });
          setStatus('authenticated');
        } else {
          localStorage.removeItem('firebase_token');
          setToken(null);
          setUser(null);
          setStatus('unauthenticated');
        }
      } catch (error) {
        console.error('Auth state change error:', error);
        setStatus('unauthenticated');
      }
    });

    return () => unsubscribe();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const userCredential = await firebaseAuthService.login(email, password);
      const token = await userCredential.user.getIdToken();
      
      localStorage.setItem('firebase_token', token);
      setToken(token);
      setUser({
        id: userCredential.user.uid,
        name: userCredential.user.displayName || email.split('@')[0] || 'User',
        email: userCredential.user.email || undefined,
        role: 'user',
        firebaseUser: userCredential.user
      });
      setStatus('authenticated');
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await firebaseAuthService.logout();
      localStorage.removeItem('firebase_token');
      setToken(null);
      setUser(null);
      setStatus('unauthenticated');
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  const value = {
    status,
    token,
    user,
    login,
    logout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext; 
