{"_from": "d3-color@1 - 2", "_id": "d3-color@2.0.0", "_inBundle": false, "_integrity": "sha512-SPXi0TSKPD4g9tw0NMZFnR95XVgUZiBH+uUTqQuDu1OsE2zomHU7ho0FISciaPvosimixwHFl3WHLGabv6dDgQ==", "_location": "/d3-zoom/d3-color", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-color@1 - 2", "name": "d3-color", "escapedName": "d3-color", "rawSpec": "1 - 2", "saveSpec": null, "fetchSpec": "1 - 2"}, "_requiredBy": ["/d3-zoom/d3-interpolate"], "_resolved": "https://registry.npmjs.org/d3-color/-/d3-color-2.0.0.tgz", "_shasum": "8d625cab42ed9b8f601a1760a389f7ea9189d62e", "_spec": "d3-color@1 - 2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\d3-zoom\\node_modules\\d3-interpolate", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-color/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Color spaces! RGB, HSL, Cubehelix, Lab and HCL (Lch).", "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-color/", "jsdelivr": "dist/d3-color.min.js", "keywords": ["d3", "d3-module", "color", "rgb", "hsl", "lab", "hcl", "lch", "cubehelix"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-color.js", "module": "src/index.js", "name": "d3-color", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-color.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src test"}, "sideEffects": false, "unpkg": "dist/d3-color.min.js", "version": "2.0.0"}