#!/usr/bin/env python3
"""
Test du chargement des variables d'environnement
"""

import os
from dotenv import load_dotenv

def test_env_loading():
    """Test du chargement du fichier .env"""
    print("🔍 TEST DU CHARGEMENT DES VARIABLES D'ENVIRONNEMENT")
    print("=" * 50)
    
    # Test 1: Chemin relatif depuis le script
    env_path1 = os.path.join(os.path.dirname(__file__), '..', '.env')
    print(f"Chemin 1: {os.path.abspath(env_path1)}")
    print(f"Existe: {os.path.exists(env_path1)}")
    
    if os.path.exists(env_path1):
        print("Contenu du fichier .env:")
        with open(env_path1, 'r') as f:
            lines = f.readlines()
            for i, line in enumerate(lines, 1):
                if 'GLOBALROAMER' in line:
                    print(f"  {i}: {line.strip()}")
    
    # Test 2: Chargement avec dotenv
    print(f"\n📁 Chargement du fichier .env...")
    load_dotenv(env_path1, override=True)
    
    # Test 3: Récupération des variables
    username = os.getenv('GLOBALROAMER_USERNAME')
    password = os.getenv('GLOBALROAMER_PASSWORD')
    
    print(f"\n🔑 Variables récupérées:")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password) if password else 'None'} ({len(password) if password else 0} caractères)")
    
    # Test 4: Vérification des valeurs par défaut
    if username and "votre_" not in username.lower() and "your_" not in username.lower():
        print("✅ Username semble valide")
    else:
        print("❌ Username semble être une valeur par défaut")
    
    if password and "votre_" not in password.lower() and "your_" not in password.lower():
        print("✅ Password semble valide")
    else:
        print("❌ Password semble être une valeur par défaut")
    
    return username, password

if __name__ == "__main__":
    test_env_loading()
