{"_from": "d3-selection@^2.0.0", "_id": "d3-selection@2.0.0", "_inBundle": false, "_integrity": "sha512-XoGGqhLUN/W14NmaqcO/bb1nqjDAw5WtSYb2X8wiuQWvSZUsUVYsOSkOybUrNvcBjaywBdYPy03eXHMXjk9nZA==", "_location": "/d3-selection", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-selection@^2.0.0", "name": "d3-selection", "escapedName": "d3-selection", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/d3-drag", "/d3-zoom", "/react-simple-maps"], "_resolved": "https://registry.npmjs.org/d3-selection/-/d3-selection-2.0.0.tgz", "_shasum": "94a11638ea2141b7565f883780dabc7ef6a61066", "_spec": "d3-selection@^2.0.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\react-simple-maps", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-selection/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Data-driven DOM manipulation: select elements and join them to data.", "devDependencies": {"eslint": "6", "jsdom": "15", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-selection/", "jsdelivr": "dist/d3-selection.min.js", "keywords": ["d3", "d3-module", "dom", "selection", "data-join"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-selection.js", "module": "src/index.js", "name": "d3-selection", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-selection.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src"}, "sideEffects": false, "unpkg": "dist/d3-selection.min.js", "version": "2.0.0"}