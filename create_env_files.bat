@echo off
echo Creation des fichiers .env...

REM Créer le fichier .env principal
echo # Configuration de la base de donnees> .env
echo DB_HOST=localhost>> .env
echo DB_PORT=3306>> .env
echo DB_USER=root>> .env
echo DB_PASSWORD=>> .env
echo DB_NAME=kpi>> .env
echo.>> .env
echo # Configuration du serveur>> .env
echo DEBUG=True>> .env
echo SECRET_KEY=your-secret-key-here>> .env
echo ALLOWED_HOSTS=localhost,127.0.0.1>> .env
echo.>> .env
echo # Configuration des chemins>> .env
echo UPLOAD_FOLDER=uploads>> .env
echo DOWNLOAD_FOLDER=downloads>> .env

REM Créer le dossier backend/scraping s'il n'existe pas
if not exist "backend\scraping" mkdir "backend\scraping"

REM Créer le fichier .env pour le scraping
echo # Configuration GlobalRoamer> backend\scraping\.env
echo GLOBALROAMER_USERNAME=meditel_ma_read>> backend\scraping\.env
echo GLOBALROAMER_PASSWORD=j(O1*54F>> backend\scraping\.env
echo.>> backend\scraping\.env
echo # Configuration des chemins>> backend\scraping\.env
echo DOWNLOAD_DIR=downloads/globalroamer>> backend\scraping\.env

echo Les fichiers .env ont ete crees avec succes!
pause 