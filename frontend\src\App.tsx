import React, { useEffect, useState } from 'react';
import { Toaster } from 'react-hot-toast';
import { FaAngleLeft, FaAngleRight, FaBars, FaBell, FaChartLine, FaGlobe, FaHome, FaSignOutAlt, FaTimes } from 'react-icons/fa';
import { createBrowserRouter, createRoutesFromElements, Link, Navigate, Route, RouterProvider, useNavigate } from 'react-router-dom';

// Import pages and components
import { AuthProvider, useAuth } from './contexts/AuthContext';
import DashboardPage from './pages/DashboardPage';
import HomePage from './pages/HomePage';
import HttpDashboardPage from './pages/HttpDashboardPage';
import InternationalRoaming from './pages/InternationalRoaming';
import LoginPage from './pages/LoginPage';
import NetworkDashboardPage from './pages/NetworkDashboardPage';
import RoamingMonitoring from './pages/RoamingMonitoring';
import RoamingSettings from './pages/RoamingSettings';
import RoamingVisualizationPage from './pages/RoamingVisualizationPage';
import SelectTablePage from './pages/SelectTablePage';
import SteeringDashboardPage from './pages/SteeringDashboardPage';
import TrendsPage from './pages/TrendsPage';

// Lazy load WorldMapPage
const WorldMapPage = React.lazy(() => import('./pages/WorldMapPage'));

// Protected route wrapper
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { status } = useAuth();
  
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500" />
      </div>
    );
  }
  
  if (status === 'unauthenticated') {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

// Public route wrapper
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { status } = useAuth();
  
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500" />
      </div>
    );
  }
  
  if (status === 'authenticated') {
    return <Navigate to="/select-table" replace />;
  }
  
  return <>{children}</>;
};

// Layout component
const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { logout, user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };
  
  const toggleSidebar = () => {
    if (windowWidth < 768) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  return (
    <div className="flex h-screen bg-gray-100 relative">
      <button 
        onClick={toggleSidebar}
        className="md:hidden fixed top-4 left-4 z-20 p-2 rounded-md bg-primary-700 text-white"
        aria-label="Toggle menu"
      >
        {sidebarOpen ? <FaTimes size={20} /> : <FaBars size={20} />}
      </button>
      
      <button
        onClick={toggleSidebar}
        className="hidden md:block fixed top-4 left-4 z-20 p-2 rounded-md bg-primary-700 text-white ml-64 transform transition-all duration-300 ease-in-out"
        style={{ marginLeft: sidebarCollapsed ? '4.5rem' : '16rem' }}
        aria-label="Collapse sidebar"
      >
        {sidebarCollapsed ? <FaAngleRight size={20} /> : <FaAngleLeft size={20} />}
      </button>
      
      <div 
        className={`transform transition-all duration-300 ease-in-out fixed md:static inset-y-0 left-0 bg-primary-800 text-white z-10 overflow-y-auto ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
        } ${
          sidebarCollapsed ? 'w-16 md:w-16' : 'w-64'
        }`}
      >
        <div className={`p-4 flex justify-between items-center ${sidebarCollapsed ? 'flex-col' : ''}`}>
          {sidebarCollapsed ? (
            <h1 className="text-2xl font-bold">GR</h1>
          ) : (
            <h1 className="text-2xl font-bold">Roaming Management</h1>
          )}
        </div>
        
        {user && !sidebarCollapsed && (
          <div className="px-4 text-sm opacity-80">
            Logged in as {user.name}
            {user.email && (
              <div className="text-xs opacity-60 mt-1">{user.email}</div>
            )}
          </div>
        )}
        
        <nav className="mt-6">
          <ul>
            <li>
              <Link 
                to="/select-table" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="Dashboard"
              >
                <FaHome className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Dashboard</span>}
              </Link>
            </li>
            <li>
              <Link 
                to="/trends" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="Trends"
              >
                <FaChartLine className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Trends</span>}
              </Link>
            </li>
            <li>
              <Link 
                to="/monitoring" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="Monitoring"
              >
                <FaBell className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Monitoring</span>}
              </Link>
            </li>
            <li>
              <Link 
                to="/international" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="International"
              >
                <FaGlobe className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>International</span>}
              </Link>
            </li>
            <li>
              <button
                onClick={handleLogout}
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700 w-full text-left`}
                title="Logout"
              >
                <FaSignOutAlt className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Logout</span>}
              </button>
            </li>
          </ul>
        </nav>
      </div>
      
      <div className="flex-1 overflow-x-hidden overflow-y-auto">
        <div className="container mx-auto px-4 py-8">
          {children}
        </div>
      </div>
    </div>
  );
};

// Router configuration
const router = createBrowserRouter(
  createRoutesFromElements(
    <Route>
      <Route
        path="/login"
        element={
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        }
      />
      <Route
        path="/"
        element={
          <ProtectedRoute>
            <Layout>
              <HomePage />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/select-table"
        element={
          <ProtectedRoute>
            <Layout>
              <SelectTablePage />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <Layout>
              <DashboardPage />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/dashboard/steering"
        element={
          <ProtectedRoute>
            <Layout>
              <SteeringDashboardPage />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/dashboard/http"
        element={
          <ProtectedRoute>
            <Layout>
              <HttpDashboardPage />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/dashboard/network"
        element={
          <ProtectedRoute>
            <Layout>
              <NetworkDashboardPage />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/dashboard/map"
        element={
          <ProtectedRoute>
            <Layout>
              <React.Suspense fallback={
                <div className="flex justify-center items-center h-screen">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500" />
                </div>
              }>
                <WorldMapPage />
              </React.Suspense>
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/trends"
        element={
          <ProtectedRoute>
            <Layout>
              <TrendsPage />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/monitoring"
        element={
          <ProtectedRoute>
            <Layout>
              <RoamingMonitoring />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/international"
        element={
          <ProtectedRoute>
            <Layout>
              <InternationalRoaming />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/settings"
        element={
          <ProtectedRoute>
            <Layout>
              <RoamingSettings />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/visualization"
        element={
          <ProtectedRoute>
            <Layout>
              <RoamingVisualizationPage />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route path="*" element={<Navigate to="/select-table" replace />} />
    </Route>
  )
);

// App component
const App: React.FC = () => {
  return (
    <>
      <AuthProvider>
        <RouterProvider router={router} />
        <Toaster />
      </AuthProvider>
    </>
  );
};

export default App;
