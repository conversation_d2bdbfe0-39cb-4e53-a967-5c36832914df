import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import React, { useEffect, useState } from 'react';
import { GeoJ<PERSON><PERSON>, MapContainer, TileLayer, useMap } from 'react-leaflet';

// Delete default icon's URL from internal object
delete (L.Icon.Default.prototype as any)._getIconUrl;

// Set default icon paths manually
L.Icon.Default.mergeOptions({
  iconRetinaUrl: '/leaflet/dist/images/marker-icon-2x.png',
  iconUrl: '/leaflet/dist/images/marker-icon.png',
  shadowUrl: '/leaflet/dist/images/marker-shadow.png',
});

interface KpiData {
  countryCode: string;
  attachmentRate: number;
}

interface WorldMapProps {
  kpiData?: KpiData[];
}

// Error Boundary Component
class MapErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): { hasError: boolean } {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Map Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center h-[600px] w-full bg-gray-100 border border-gray-300 rounded">
          <div className="text-center">
            <p className="text-gray-600 mb-2">Unable to load map</p>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// MapContent component to handle map-specific logic
const MapContent: React.FC<{
  geoJsonData: any;
  kpiData?: KpiData[];
}> = ({ geoJsonData, kpiData }) => {
  const map = useMap();

  const getColor = (attachmentRate: number) => {
    if (attachmentRate >= 90) return '#2ecc71';
    if (attachmentRate >= 70) return '#f1c40f';
    return '#e74c3c';
  };

  const countryStyle = (feature: any) => {
    const countryCode = feature.properties.ISO_A2;
    const countryData = kpiData?.find(data => data.countryCode === countryCode);
    
    return {
      fillColor: countryData ? getColor(countryData.attachmentRate) : '#95a5a6',
      weight: 1,
      opacity: 1,
      color: 'white',
      dashArray: '3',
      fillOpacity: 0.7
    };
  };

  const onEachFeature = (feature: any, layer: L.Layer) => {
    if (layer instanceof L.Path) {
      const countryCode = feature.properties.ISO_A2;
      const countryData = kpiData?.find(data => data.countryCode === countryCode);
      
      layer.bindPopup(`
        <strong>${feature.properties.NAME}</strong><br/>
        ${countryData 
          ? `Attachment Rate: ${countryData.attachmentRate}%` 
          : 'No data available'}
      `);
    }
  };

  useEffect(() => {
    if (map) {
      map.invalidateSize();
    }
  }, [map]);

  return (
    <>
      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      {geoJsonData && (
        <GeoJSON
          key={JSON.stringify(geoJsonData)} // Add key to force re-render when data changes
          data={geoJsonData}
          style={countryStyle}
          onEachFeature={onEachFeature}
        />
      )}
    </>
  );
};

const WorldMap: React.FC<WorldMapProps> = ({ kpiData }) => {
  const [geoJsonData, setGeoJsonData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [mapKey, setMapKey] = useState(0);

  useEffect(() => {
    const loadGeoJson = async () => {
      try {
        const data = await import('../assets/custom.geo.json');
        setGeoJsonData(data.default);
      } catch (error) {
        console.error('Error loading GeoJSON:', error);
        setError('Failed to load map data');
      }
    };

    loadGeoJson();
  }, []);

  // Force re-render of map when there's an error
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
        setMapKey(prev => prev + 1);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-[600px] w-full">
        <div className="text-red-500 text-center">
          <p>{error}</p>
          <p className="text-sm text-gray-500 mt-2">Retrying...</p>
        </div>
      </div>
    );
  }

  if (!geoJsonData) {
    return (
      <div className="flex items-center justify-center h-[600px] w-full">
        <div className="text-gray-500">Loading map...</div>
      </div>
    );
  }

  return (
    <MapErrorBoundary>
      <div className="relative h-[600px] w-full">
        <MapContainer
          key={`world-map-${mapKey}`}
          center={[20, 0]}
          zoom={2}
          className="h-full w-full"
          scrollWheelZoom={true}
          style={{ height: '100%', width: '100%' }}
        >
          <MapContent geoJsonData={geoJsonData} kpiData={kpiData} />
        </MapContainer>
      </div>
    </MapErrorBoundary>
  );
};

export default WorldMap; 