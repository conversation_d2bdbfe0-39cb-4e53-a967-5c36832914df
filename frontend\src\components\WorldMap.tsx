import axios from 'axios';
import React, { useEffect, useState } from 'react';
import {
    ComposableMap,
    Geographies,
    Geography,
    GeographyProps,
    ZoomableGroup
} from 'react-simple-maps';
import { API_BASE_URL } from '../config';

// URL vers un fichier GeoJSON fiable
const geoUrl = "https://cdn.jsdelivr.net/npm/world-atlas@2/countries-110m.json";

interface Anomaly {
  country: string;
  attach_success: number;
  severity: string;
  causes: string[];
  primary_cause: string;
  details: {
    attach_success?: {
      current: number;
      previous_avg: number;
      change_percent: number;
    };
    drop_rate?: {
      current: number;
      previous_avg: number;
      change: number;
    };
    latency?: {
      current: number;
      previous_avg: number;
      change_percent: number;
    };
  };
}

interface CountryData {
  name: string;
  anomaly?: Anomaly;
}

const getSeverityColor = (severity: string, isHovered: boolean): string => {
  if (isHovered) return '#3388ff';
  
  switch (severity?.toLowerCase()) {
    case 'high':
      return '#ff4444';
    case 'medium':
      return '#ffbb33';
    case 'low':
      return '#00C851';
    default:
      return '#D6D6DA';
  }
};

const formatValue = (value: number): string => {
  return value.toFixed(2);
};

const WorldMap: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [hoveredCountry, setHoveredCountry] = useState<CountryData | null>(null);
  const [anomalies, setAnomalies] = useState<Record<string, Anomaly>>({});
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchAnomalies = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`${API_BASE_URL}/kpi/alerts`);
        
        // Transformer les anomalies en map par pays
        const anomaliesMap = response.data.anomalies.reduce((acc: Record<string, Anomaly>, anomaly: Anomaly) => {
          acc[anomaly.country] = anomaly;
          return acc;
        }, {});
        
        setAnomalies(anomaliesMap);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erreur lors du chargement des anomalies');
      } finally {
        setLoading(false);
      }
    };

    fetchAnomalies();
    // Rafraîchir les données toutes les 5 minutes
    const interval = setInterval(fetchAnomalies, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  if (error) {
    return (
      <div className="h-[600px] w-full flex items-center justify-center bg-gray-100">
        <div className="text-red-500">
          Erreur lors du chargement de la carte: {error}
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="h-[600px] w-full flex items-center justify-center bg-gray-100">
        <div className="text-gray-600">
          Chargement des données...
        </div>
      </div>
    );
  }

  return (
    <div className="h-[600px] w-full relative">
      <ComposableMap
        projectionConfig={{
          scale: 147,
          center: [0, 0]
        }}
      >
        <ZoomableGroup>
          <Geographies geography={geoUrl}>
            {({ geographies }) =>
              geographies.map((geo) => {
                const countryName = geo.properties.name;
                const anomaly = anomalies[countryName];
                const isHovered = hoveredCountry?.name === countryName;
                
                return (
                  <Geography<GeographyProps>
                    key={geo.rsmKey}
                    geography={geo}
                    fill={getSeverityColor(anomaly?.severity, isHovered)}
                    stroke="#FFFFFF"
                    style={{
                      default: {
                        outline: 'none'
                      },
                      hover: {
                        fill: '#3388ff',
                        outline: 'none'
                      },
                      pressed: {
                        fill: '#2166ac',
                        outline: 'none'
                      }
                    }}
                    onMouseEnter={() => {
                      setHoveredCountry({
                        name: countryName,
                        anomaly
                      });
                    }}
                    onMouseLeave={() => {
                      setHoveredCountry(null);
                    }}
                  />
                );
              })
            }
          </Geographies>
          {hoveredCountry && (
            <foreignObject
              x={0}
              y={0}
              width="100%"
              height="100%"
              style={{ pointerEvents: 'none' }}
            >
              <div
                className="absolute left-1/2 top-4 transform -translate-x-1/2 bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-md"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                  zIndex: 1000
                }}
              >
                <h3 className="text-lg font-semibold mb-2">{hoveredCountry.name}</h3>
                {hoveredCountry.anomaly ? (
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="font-medium">Taux d'attache:</span>
                      <span className={`font-bold ${
                        hoveredCountry.anomaly.attach_success >= 90 ? 'text-green-600' :
                        hoveredCountry.anomaly.attach_success >= 75 ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {formatValue(hoveredCountry.anomaly.attach_success)}%
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="font-medium">Sévérité:</span>
                      <span className={`font-bold ${
                        hoveredCountry.anomaly.severity === 'high' ? 'text-red-600' :
                        hoveredCountry.anomaly.severity === 'medium' ? 'text-yellow-600' :
                        'text-green-600'
                      }`}>
                        {hoveredCountry.anomaly.severity.toUpperCase()}
                      </span>
                    </div>
                    
                    <div>
                      <span className="font-medium">Cause principale:</span>
                      <p className="mt-1 text-gray-700">{hoveredCountry.anomaly.primary_cause}</p>
                    </div>
                    
                    {hoveredCountry.anomaly.details.attach_success && (
                      <div className="mt-2 p-2 bg-gray-50 rounded">
                        <div className="text-xs text-gray-600">
                          Variation du taux d'attache: {
                            formatValue(hoveredCountry.anomaly.details.attach_success.change_percent)
                          }%
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-sm text-gray-600">
                    Aucune anomalie détectée
                  </div>
                )}
              </div>
            </foreignObject>
          )}
        </ZoomableGroup>
      </ComposableMap>
      
      {/* Légende */}
      <div className="absolute bottom-4 right-4 bg-white p-3 rounded-lg shadow-md">
        <h4 className="text-sm font-semibold mb-2">Sévérité des anomalies</h4>
        <div className="space-y-1">
          <div className="flex items-center">
            <div className="w-4 h-4 rounded bg-red-500 mr-2"></div>
            <span className="text-xs">Haute</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 rounded bg-yellow-500 mr-2"></div>
            <span className="text-xs">Moyenne</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 rounded bg-green-500 mr-2"></div>
            <span className="text-xs">Faible</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 rounded bg-gray-300 mr-2"></div>
            <span className="text-xs">Aucune anomalie</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorldMap; 