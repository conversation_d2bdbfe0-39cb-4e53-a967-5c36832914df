import os
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

def init_ml_environment():
    """
    Initialise l'environnement pour le machine learning
    - Crée les dossiers nécessaires
    - Configure les chemins
    """
    try:
        # Définir les chemins des dossiers
        base_path = Path(__file__).parent.parent
        models_path = base_path / "models"
        data_path = base_path / "data"
        
        # Créer les dossiers s'ils n'existent pas
        os.makedirs(models_path, exist_ok=True)
        os.makedirs(data_path, exist_ok=True)
        
        logger.info("Environnement ML initialisé avec succès")
        
        return {
            "models_path": str(models_path),
            "data_path": str(data_path)
        }
        
    except Exception as e:
        logger.error(f"Erreur lors de l'initialisation de l'environnement ML: {str(e)}")
        raise 