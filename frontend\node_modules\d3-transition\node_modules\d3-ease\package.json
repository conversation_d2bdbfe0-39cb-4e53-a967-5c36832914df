{"_from": "d3-ease@1 - 2", "_id": "d3-ease@2.0.0", "_inBundle": false, "_integrity": "sha512-68/n9JWarxXkOWMshcT5IcjbB+agblQUaIsbnXmrzejn2O82n3p2A9R2zEB9HIEFWKFwPAEDDN8gR0VdSAyyAQ==", "_location": "/d3-transition/d3-ease", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-ease@1 - 2", "name": "d3-ease", "escapedName": "d3-ease", "rawSpec": "1 - 2", "saveSpec": null, "fetchSpec": "1 - 2"}, "_requiredBy": ["/d3-transition"], "_resolved": "https://registry.npmjs.org/d3-ease/-/d3-ease-2.0.0.tgz", "_shasum": "fd1762bfca00dae4bacea504b1d628ff290ac563", "_spec": "d3-ease@1 - 2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\d3-transition", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-ease/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Easing functions for smooth animation.", "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-ease/", "jsdelivr": "dist/d3-ease.min.js", "keywords": ["d3", "d3-module", "ease", "easing", "animation", "transition"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-ease.js", "module": "src/index.js", "name": "d3-ease", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-ease.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src test"}, "sideEffects": false, "unpkg": "dist/d3-ease.min.js", "version": "2.0.0"}