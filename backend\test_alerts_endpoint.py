#!/usr/bin/env python3
"""
Test de l'endpoint /api/kpi/alerts pour vérifier les alertes basées sur les données réelles
"""

import requests
import json
import logging
from datetime import datetime

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_alerts_endpoint():
    """Tester l'endpoint /api/kpi/alerts"""
    logger.info("🚨 TEST DE L'ENDPOINT /api/kpi/alerts")
    logger.info("=" * 60)
    
    try:
        # Appeler l'endpoint
        url = "http://localhost:8000/api/kpi/alerts"
        response = requests.get(url)
        
        if response.status_code == 200:
            alerts = response.json()
            
            logger.info(f"✅ Endpoint accessible - Status: {response.status_code}")
            logger.info(f"📊 {len(alerts)} alertes trouvées")
            
            if alerts:
                logger.info(f"\n🚨 DÉTAIL DES ALERTES:")
                logger.info("-" * 50)
                
                # Grouper par sévérité
                high_alerts = [a for a in alerts if a.get('severity') == 'high']
                medium_alerts = [a for a in alerts if a.get('severity') == 'medium']
                low_alerts = [a for a in alerts if a.get('severity') == 'low']
                
                # Afficher les alertes HIGH
                if high_alerts:
                    logger.info(f"🔴 ALERTES CRITIQUES ({len(high_alerts)}):")
                    for alert in high_alerts:
                        logger.info(f"   • {alert['country']}: {alert['description']}")
                        logger.info(f"     Taux succès: {alert['success_rate']}% | Taux échec: {alert['failure_rate']}%")
                
                # Afficher les alertes MEDIUM
                if medium_alerts:
                    logger.info(f"\n🟡 ALERTES MOYENNES ({len(medium_alerts)}):")
                    for alert in medium_alerts:
                        logger.info(f"   • {alert['country']}: {alert['description']}")
                        logger.info(f"     Taux succès: {alert['success_rate']}% | Taux échec: {alert['failure_rate']}%")
                
                # Afficher les alertes LOW
                if low_alerts:
                    logger.info(f"\n🟢 ALERTES MINEURES ({len(low_alerts)}):")
                    for alert in low_alerts:
                        logger.info(f"   • {alert['country']}: {alert['description']}")
                        logger.info(f"     Tests: {alert.get('total_tests', 'N/A')}")
                
                # Statistiques des alertes
                logger.info(f"\n📈 STATISTIQUES DES ALERTES:")
                logger.info("-" * 50)
                
                alert_types = {}
                for alert in alerts:
                    alert_type = alert.get('type_alert', 'unknown')
                    alert_types[alert_type] = alert_types.get(alert_type, 0) + 1
                
                for alert_type, count in alert_types.items():
                    logger.info(f"   • {alert_type}: {count} alertes")
                
                # Pays les plus problématiques
                country_alerts = {}
                for alert in alerts:
                    country = alert.get('country', 'Unknown')
                    if country not in country_alerts:
                        country_alerts[country] = []
                    country_alerts[country].append(alert)
                
                logger.info(f"\n🌍 PAYS LES PLUS PROBLÉMATIQUES:")
                logger.info("-" * 50)
                
                sorted_countries = sorted(country_alerts.items(), key=lambda x: len(x[1]), reverse=True)
                for country, country_alert_list in sorted_countries[:5]:
                    logger.info(f"   • {country}: {len(country_alert_list)} alerte(s)")
                    for alert in country_alert_list:
                        logger.info(f"     - {alert['type_alert']} ({alert['severity']})")
            
            else:
                logger.info("✅ Aucune alerte détectée - Tous les pays fonctionnent correctement")
            
            return True
            
        else:
            logger.error(f"❌ Erreur HTTP: {response.status_code}")
            logger.error(f"Réponse: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        logger.error("❌ Impossible de se connecter au serveur backend")
        logger.error("💡 Assurez-vous que le serveur backend est démarré (uvicorn main:app)")
        return False
    except Exception as e:
        logger.error(f"❌ Erreur lors du test: {e}")
        return False

def compare_with_steering_success():
    """Comparer avec l'endpoint steering_success_by_country pour validation"""
    logger.info(f"\n🔍 COMPARAISON AVEC LES DONNÉES DE BASE")
    logger.info("=" * 60)
    
    try:
        # Récupérer les données de base
        url = "http://localhost:8000/api/steering_success_by_country_filtered"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            steering_data = data.get('data', [])
            
            logger.info(f"📊 {len(steering_data)} pays dans les données de base")
            
            # Afficher les 5 pays avec les plus faibles taux de succès
            if steering_data:
                sorted_countries = sorted(steering_data, key=lambda x: x.get('success_rate', 0))
                
                logger.info(f"\n📉 TOP 5 PAYS AVEC LES PLUS FAIBLES TAUX DE SUCCÈS:")
                logger.info("-" * 50)
                
                for country in sorted_countries[:5]:
                    logger.info(f"   • {country.get('a_location_country', 'Unknown')}: {country.get('success_rate', 0):.2f}%")
                    logger.info(f"     Tests: {country.get('total_tests', 0)} | Échecs: {country.get('failure_rate', 0):.2f}%")
                
                # Vérifier la cohérence avec les alertes
                logger.info(f"\n✅ Les alertes sont générées à partir de ces données réelles")
            
            return True
        else:
            logger.error(f"❌ Erreur lors de la récupération des données de base: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erreur lors de la comparaison: {e}")
        return False

def main():
    """Fonction principale"""
    logger.info("🚀 TEST COMPLET DE L'ENDPOINT /api/kpi/alerts")
    logger.info("=" * 70)
    logger.info(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test de l'endpoint alerts
    alerts_ok = test_alerts_endpoint()
    
    # Comparaison avec les données de base
    comparison_ok = compare_with_steering_success()
    
    # Résumé final
    logger.info(f"\n🎯 RÉSUMÉ FINAL")
    logger.info("=" * 70)
    
    if alerts_ok and comparison_ok:
        logger.info("🎉 ENDPOINT /api/kpi/alerts FONCTIONNE PARFAITEMENT!")
        logger.info("✅ Les alertes sont basées sur les vraies données de la base")
        logger.info("✅ L'endpoint utilise steering_success_by_country_filtered")
        logger.info("✅ Les calculs de taux de succès sont corrects")
    elif alerts_ok:
        logger.info("✅ Endpoint alerts fonctionnel")
        logger.warning("⚠️ Problème avec la comparaison des données")
    else:
        logger.error("❌ Problème avec l'endpoint alerts")
    
    return alerts_ok and comparison_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
