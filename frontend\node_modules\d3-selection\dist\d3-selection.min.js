// https://d3js.org/d3-selection/ v2.0.0 Copyright 2020 Mike <PERSON>
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t=t||self).d3=t.d3||{})}(this,function(t){"use strict";var n="http://www.w3.org/1999/xhtml",e={svg:"http://www.w3.org/2000/svg",xhtml:n,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function r(t){var n=t+="",r=n.indexOf(":");return r>=0&&"xmlns"!==(n=t.slice(0,r))&&(t=t.slice(r+1)),e.hasOwnProperty(n)?{space:e[n],local:t}:t}function i(t){var e=r(t);return(e.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var e=this.ownerDocument,r=this.namespaceURI;return r===n&&e.documentElement.namespaceURI===n?e.createElement(t):e.createElementNS(r,t)}})(e)}function o(){}function u(t){return null==t?o:function(){return this.querySelector(t)}}function c(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function s(){return[]}function l(t){return null==t?s:function(){return this.querySelectorAll(t)}}function a(t){return function(){return this.matches(t)}}function f(t){return function(n){return n.matches(t)}}var h=Array.prototype.find;function p(){return this.firstElementChild}var _=Array.prototype.filter;function d(){return this.children}function y(t){return new Array(t.length)}function v(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function m(t,n,e,r,i,o){for(var u,c=0,s=n.length,l=o.length;c<l;++c)(u=n[c])?(u.__data__=o[c],r[c]=u):e[c]=new v(t,o[c]);for(;c<s;++c)(u=n[c])&&(i[c]=u)}function g(t,n,e,r,i,o,u){var c,s,l,a=new Map,f=n.length,h=o.length,p=new Array(f);for(c=0;c<f;++c)(s=n[c])&&(p[c]=l=u.call(s,s.__data__,c,n)+"",a.has(l)?i[c]=s:a.set(l,s));for(c=0;c<h;++c)l=u.call(t,o[c],c,o)+"",(s=a.get(l))?(r[c]=s,s.__data__=o[c],a.delete(l)):e[c]=new v(t,o[c]);for(c=0;c<f;++c)(s=n[c])&&a.get(p[c])===s&&(i[c]=s)}function w(t){return t.__data__}function A(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}function x(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function S(t,n){return t.style.getPropertyValue(n)||x(t).getComputedStyle(t,null).getPropertyValue(n)}function b(t){return t.trim().split(/^|\s+/)}function E(t){return t.classList||new N(t)}function N(t){this._node=t,this._names=b(t.getAttribute("class")||"")}function C(t,n){for(var e=E(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function L(t,n){for(var e=E(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function P(){this.textContent=""}function B(){this.innerHTML=""}function M(){this.nextSibling&&this.parentNode.appendChild(this)}function T(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function q(){return null}function D(){var t=this.parentNode;t&&t.removeChild(this)}function O(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function V(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function j(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)e=n[r],t.type&&e.type!==t.type||e.name!==t.name?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function R(t,n,e){return function(){var r,i=this.__on,o=function(t){return function(n){t.call(this,n,this.__data__)}}(n);if(i)for(var u=0,c=i.length;u<c;++u)if((r=i[u]).type===t.type&&r.name===t.name)return this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),void(r.value=n);this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}function H(t,n,e){var r=x(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}v.prototype={constructor:v,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}},N.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var I=[null];function U(t,n){this._groups=t,this._parents=n}function X(){return new U([[document.documentElement]],I)}function G(t){return"string"==typeof t?new U([[document.querySelector(t)]],[document.documentElement]):new U([[t]],I)}U.prototype=X.prototype={constructor:U,select:function(t){"function"!=typeof t&&(t=u(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,c,s=n[i],l=s.length,a=r[i]=new Array(l),f=0;f<l;++f)(o=s[f])&&(c=t.call(o,o.__data__,f,s))&&("__data__"in o&&(c.__data__=o.__data__),a[f]=c);return new U(r,this._parents)},selectAll:function(t){t="function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);return null==n?[]:c(n)}}(t):l(t);for(var n=this._groups,e=n.length,r=[],i=[],o=0;o<e;++o)for(var u,s=n[o],a=s.length,f=0;f<a;++f)(u=s[f])&&(r.push(t.call(u,u.__data__,f,s)),i.push(u));return new U(r,i)},selectChild:function(t){return this.select(null==t?p:function(t){return function(){return h.call(this.children,t)}}("function"==typeof t?t:f(t)))},selectChildren:function(t){return this.selectAll(null==t?d:function(t){return function(){return _.call(this.children,t)}}("function"==typeof t?t:f(t)))},filter:function(t){"function"!=typeof t&&(t=a(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,u=n[i],c=u.length,s=r[i]=[],l=0;l<c;++l)(o=u[l])&&t.call(o,o.__data__,l,u)&&s.push(o);return new U(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,w);var e,r=n?g:m,i=this._parents,o=this._groups;"function"!=typeof t&&(e=t,t=function(){return e});for(var u=o.length,s=new Array(u),l=new Array(u),a=new Array(u),f=0;f<u;++f){var h=i[f],p=o[f],_=p.length,d=c(t.call(h,h&&h.__data__,f,i)),y=d.length,v=l[f]=new Array(y),A=s[f]=new Array(y);r(h,p,v,A,a[f]=new Array(_),d,n);for(var x,S,b=0,E=0;b<y;++b)if(x=v[b]){for(b>=E&&(E=b+1);!(S=A[E])&&++E<y;);x._next=S||null}}return(s=new U(s,i))._enter=l,s._exit=a,s},enter:function(){return new U(this._enter||this._groups.map(y),this._parents)},exit:function(){return new U(this._exit||this._groups.map(y),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return r="function"==typeof t?t(r):r.append(t+""),null!=n&&(i=n(i)),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){if(!(t instanceof U))throw new Error("invalid merge");for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),c=0;c<o;++c)for(var s,l=n[c],a=e[c],f=l.length,h=u[c]=new Array(f),p=0;p<f;++p)(s=l[p]||a[p])&&(h[p]=s);for(;c<r;++c)u[c]=n[c];return new U(u,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=A);for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o){for(var u,c=e[o],s=c.length,l=i[o]=new Array(s),a=0;a<s;++a)(u=c[a])&&(l[a]=u);l.sort(n)}return new U(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){let t=0;for(const n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],u=0,c=o.length;u<c;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,n){var e=r(t);if(arguments.length<2){var i=this.node();return e.local?i.getAttributeNS(e.space,e.local):i.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):S(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=b(t+"");if(arguments.length<2){for(var r=E(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?C:L)(this,t)}}:n?function(t){return function(){C(this,t)}}:function(t){return function(){L(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?P:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?B:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(M)},lower:function(){return this.each(T)},append:function(t){var n="function"==typeof t?t:i(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:i(t),r=null==n?q:"function"==typeof n?n:u(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(D)},clone:function(t){return this.select(t?V:O)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=function(t){return t.trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}})}(t+""),u=o.length;if(!(arguments.length<2)){for(c=n?R:j,r=0;r<u;++r)this.each(c(o[r],n,e));return this}var c=this.node().__on;if(c)for(var s,l=0,a=c.length;l<a;++l)for(r=0,s=c[l];r<u;++r)if((i=o[r]).type===s.type&&i.name===s.name)return s.value},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return H(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return H(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,u=i.length;o<u;++o)(r=i[o])&&(yield r)}};var Y=0;function k(){return new z}function z(){this._="@"+(++Y).toString(36)}function F(t){let n;for(;n=t.sourceEvent;)t=n;return t}function J(t,n){if(t=F(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}z.prototype=k.prototype={constructor:z,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}},t.create=function(t){return G(i(t).call(document.documentElement))},t.creator=i,t.local=k,t.matcher=a,t.namespace=r,t.namespaces=e,t.pointer=J,t.pointers=function(t,n){return t.target&&(t=F(t),void 0===n&&(n=t.currentTarget),t=t.touches||[t]),Array.from(t,t=>J(t,n))},t.select=G,t.selectAll=function(t){return"string"==typeof t?new U([document.querySelectorAll(t)],[document.documentElement]):new U([null==t?[]:c(t)],I)},t.selection=X,t.selector=u,t.selectorAll=l,t.style=S,t.window=x,Object.defineProperty(t,"__esModule",{value:!0})});
