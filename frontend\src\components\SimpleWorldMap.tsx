import React, { useEffect, useState } from 'react';

interface KpiData {
  countryCode: string;
  attachmentRate: number;
}

interface SimpleWorldMapProps {
  kpiData?: KpiData[];
}

const SimpleWorldMap: React.FC<SimpleWorldMapProps> = ({ kpiData }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [realData, setRealData] = useState<KpiData[]>([]);

  useEffect(() => {
    const fetchRealData = async () => {
      try {
        setIsLoading(true);

        // Essayer de récupérer les données réelles depuis l'API
        const response = await fetch('/api/steering/countries');
        if (response.ok) {
          const data = await response.json();
          // Transformer les données pour correspondre à notre interface
          const transformedData = data.map((item: any) => ({
            countryCode: item.country_code || item.countryCode,
            attachmentRate: Math.round(item.success_rate || item.attachmentRate || 0)
          }));
          setRealData(transformedData);
        } else {
          // Utiliser les données mock si l'API échoue
          setRealData(kpiData || []);
        }
      } catch (error) {
        console.warn('Erreur lors du chargement des données réelles, utilisation des données mock:', error);
        setRealData(kpiData || []);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRealData();
  }, [kpiData]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-[600px] w-full bg-gray-100 border border-gray-300 rounded">
        <div className="text-center">
          <p className="text-red-600 mb-2">Unable to load map</p>
          <button
            onClick={() => {
              setError(null);
              setIsLoading(true);
              setTimeout(() => setIsLoading(false), 1000);
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[600px] w-full bg-gray-50 border border-gray-300 rounded">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading world map...</p>
        </div>
      </div>
    );
  }

  // Mapping des codes pays vers les noms complets
  const countryNames: { [key: string]: string } = {
    'FR': 'France',
    'DE': 'Germany',
    'ES': 'Spain',
    'IT': 'Italy',
    'GB': 'United Kingdom',
    'US': 'United States',
    'MA': 'Morocco',
    'TN': 'Tunisia',
    'DZ': 'Algeria',
    'CRI': 'Costa Rica',
    'NIC': 'Nicaragua',
    'HTI': 'Haiti',
    'DOM': 'Dominican Republic',
    'SLV': 'El Salvador',
    'GTM': 'Guatemala',
    'CUB': 'Cuba',
    'HND': 'Honduras',
    'SN': 'Senegal'
  };

  // Fonction pour obtenir la couleur selon le taux
  const getColorClass = (rate: number) => {
    if (rate >= 90) return 'bg-green-500';
    if (rate >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // Préparer les données des pays avec les vraies données
  const countries = realData.map(item => ({
    name: countryNames[item.countryCode] || item.countryCode,
    rate: item.attachmentRate,
    color: getColorClass(item.attachmentRate),
    code: item.countryCode
  })).sort((a, b) => b.rate - a.rate); // Trier par taux décroissant

  return (
    <div className="h-[600px] w-full bg-gray-100 border border-gray-300 rounded p-6">
      <div className="h-full flex flex-col">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Global Roaming Performance Map
          </h3>
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              Interactive map temporarily unavailable. Showing country statistics below.
            </p>
            <div className="flex items-center text-xs">
              <div className={`w-2 h-2 rounded-full mr-2 ${realData.length > 0 ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
              <span className="text-gray-500">
                {realData.length > 0 ? 'Live Data' : 'Sample Data'}
              </span>
            </div>
          </div>
        </div>

        <div className="flex-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {countries.length > 0 ? countries.map((country) => (
            <div
              key={country.code || country.name}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between mb-2">
                <div>
                  <h4 className="font-medium text-gray-800">{country.name}</h4>
                  <span className="text-xs text-gray-500">{country.code}</span>
                </div>
                <div className={`w-3 h-3 rounded-full ${country.color}`}></div>
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {country.rate}%
              </div>
              <div className="text-xs text-gray-500">Attachment Rate</div>
              <div className="mt-2 bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${country.color}`}
                  style={{ width: `${Math.min(country.rate, 100)}%` }}
                ></div>
              </div>
            </div>
          )) : (
            <div className="col-span-full text-center text-gray-500 py-8">
              <p>No country data available</p>
            </div>
          )}
        </div>

        <div className="mt-4 flex justify-center">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span>Excellent (90%+)</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
              <span>Good (70-89%)</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
              <span>Poor (&lt;70%)</span>
            </div>
          </div>
        </div>

        <div className="mt-4 text-center">
          <button
            onClick={() => {
              setError('Map component temporarily disabled');
            }}
            className="text-blue-600 hover:text-blue-800 text-sm underline"
          >
            Try to load interactive map
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleWorldMap;
