import React, { useEffect, useState } from 'react';

interface KpiData {
  countryCode: string;
  attachmentRate: number;
}

interface SimpleWorldMapProps {
  kpiData?: KpiData[];
}

const SimpleWorldMap: React.FC<SimpleWorldMapProps> = ({ kpiData }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (error) {
    return (
      <div className="flex items-center justify-center h-[600px] w-full bg-gray-100 border border-gray-300 rounded">
        <div className="text-center">
          <p className="text-red-600 mb-2">Unable to load map</p>
          <button
            onClick={() => {
              setError(null);
              setIsLoading(true);
              setTimeout(() => setIsLoading(false), 1000);
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[600px] w-full bg-gray-50 border border-gray-300 rounded">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading world map...</p>
        </div>
      </div>
    );
  }

  // Simple placeholder map with country statistics
  const countries = [
    { name: 'France', rate: 95, color: 'bg-green-500' },
    { name: 'Germany', rate: 88, color: 'bg-green-400' },
    { name: 'Spain', rate: 92, color: 'bg-green-500' },
    { name: 'Italy', rate: 85, color: 'bg-yellow-500' },
    { name: 'UK', rate: 90, color: 'bg-green-400' },
    { name: 'Morocco', rate: 78, color: 'bg-yellow-500' },
    { name: 'Tunisia', rate: 82, color: 'bg-yellow-500' },
    { name: 'Algeria', rate: 75, color: 'bg-yellow-600' },
  ];

  return (
    <div className="h-[600px] w-full bg-gray-100 border border-gray-300 rounded p-6">
      <div className="h-full flex flex-col">
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-800 mb-2">
            Global Roaming Performance Map
          </h3>
          <p className="text-sm text-gray-600">
            Interactive map temporarily unavailable. Showing country statistics below.
          </p>
        </div>

        <div className="flex-1 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {countries.map((country) => (
            <div
              key={country.name}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-800">{country.name}</h4>
                <div className={`w-3 h-3 rounded-full ${country.color}`}></div>
              </div>
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {country.rate}%
              </div>
              <div className="text-xs text-gray-500">Attachment Rate</div>
              <div className="mt-2 bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${country.color}`}
                  style={{ width: `${country.rate}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-4 flex justify-center">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span>Excellent (90%+)</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
              <span>Good (70-89%)</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
              <span>Poor (&lt;70%)</span>
            </div>
          </div>
        </div>

        <div className="mt-4 text-center">
          <button
            onClick={() => {
              setError('Map component temporarily disabled');
            }}
            className="text-blue-600 hover:text-blue-800 text-sm underline"
          >
            Try to load interactive map
          </button>
        </div>
      </div>
    </div>
  );
};

export default SimpleWorldMap;
