"""
Module de gestion des indicateurs de performance (KPI)
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import pandas as pd
from sqlalchemy import create_engine
from dotenv import load_dotenv
import os
import pymysql # Importer pymysql
import logging
import json

from ..models import User, KpiWeek, KpiData
from ..routers.auth import get_current_user
# Import des fonctions du service KPI
try:
    from ..Kpi_service_clean import (
        calculate_success_rate, 
        get_fail_causes, 
        get_weekly_attach_trend,
        get_annual_trend,
        get_failure_analysis_by_filter,
        get_country_overview,
        get_steering_success_by_country,
        get_attachment_rates_by_country,
        get_steering_data,
        get_failure_details
    )
except ImportError:
    print("ATTENTION: Impossible d'importer depuis Kpi_service_clean, tentative avec Kpi_service_new...")
    try:
        from ..Kpi_service_new import (
            calculate_success_rate, 
            get_fail_causes, 
            get_weekly_attach_trend,
            get_annual_trend,
            get_failure_analysis_by_filter,
            get_country_overview,
            get_steering_success_by_country,
            get_attachment_rates_by_country,
            get_steering_data,
            get_failure_details
        )
    except ImportError:
        print("ERREUR: Impossible d'importer les services KPI nécessaires")

from ..services.ml_service import MLService
from ..ml.kpi_predictor import KPIAnomalyDetector
from ..services.csv_processing import process_csv_file
from ..services.kpi_alerts_service import KpiAlertsService

# Configuration du router
router = APIRouter()
ml_service = MLService()
kpi_alerts_service = KpiAlertsService()
logger = logging.getLogger(__name__)

# Base de données simulée pour les semaines
weeks_db = {
    "1": {
        "id": "1",
        "name": "Semaine 1 - 2023",
        "start_date": "2023-01-01",
        "end_date": "2023-01-07"
    },
    "2": {
        "id": "2",
        "name": "Semaine 2 - 2023",
        "start_date": "2023-01-08",
        "end_date": "2023-01-14"
    },
    "3": {
        "id": "3",
        "name": "Semaine 3 - 2023",
        "start_date": "2023-01-15",
        "end_date": "2023-01-21"
    }
}

# Base de données simulée pour les KPI
kpi_data_db = {
    "1": {
        "id": "1",
        "week_id": "1",
        "timestamp": datetime.now().isoformat(),
        "kpis": {
            "voice": {
                "total_calls": 12500,
                "average_duration": 95.5,  # secondes
                "success_rate": 98.2,  # pourcentage
                "revenue": 4500.75
            },
            "data": {
                "total_volume": 45000,  # MB
                "average_speed": 15.2,  # Mbps
                "success_rate": 99.5,  # pourcentage
                "revenue": 15750.50
            },
            "sms": {
                "total_messages": 35000,
                "delivery_rate": 99.8,  # pourcentage
                "revenue": 2100.25
            }
        },
        "top_partners": [
            {"name": "Orange France", "traffic": 3500, "revenue": 1250.50},
            {"name": "Vodafone UK", "traffic": 2800, "revenue": 980.75},
            {"name": "Deutsche Telekom", "traffic": 2200, "revenue": 770.25}
        ],
        "service_quality": {
            "voice_quality_index": 4.2,  # sur 5
            "data_quality_index": 4.5,  # sur 5
            "overall_satisfaction": 4.3  # sur 5
        }
    },
    "2": {
        "id": "2",
        "week_id": "2",
        "timestamp": datetime.now().isoformat(),
        "kpis": {
            "voice": {
                "total_calls": 13200,
                "average_duration": 93.8,  # secondes
                "success_rate": 98.5,  # pourcentage
                "revenue": 4680.50
            },
            "data": {
                "total_volume": 48500,  # MB
                "average_speed": 15.8,  # Mbps
                "success_rate": 99.6,  # pourcentage
                "revenue": 16975.25
            },
            "sms": {
                "total_messages": 33500,
                "delivery_rate": 99.9,  # pourcentage
                "revenue": 2010.00
            }
        },
        "top_partners": [
            {"name": "Orange France", "traffic": 3650, "revenue": 1277.50},
            {"name": "Vodafone UK", "traffic": 3000, "revenue": 1050.00},
            {"name": "Deutsche Telekom", "traffic": 2350, "revenue": 822.50}
        ],
        "service_quality": {
            "voice_quality_index": 4.3,  # sur 5
            "data_quality_index": 4.6,  # sur 5
            "overall_satisfaction": 4.4  # sur 5
        }
    }
}

# Fonctions pour la table SteeringOfRoaming
load_dotenv()

# Routes pour les semaines et les données KPI existantes
@router.get("/weeks", response_model=List[KpiWeek])
async def get_weeks(current_user: User = Depends(get_current_user)):
    """Récupérer toutes les semaines disponibles pour les KPI"""
    try:
        weeks = [
            KpiWeek(
                id=week_id,
                name=week_data["name"],
                start_date=week_data["start_date"],
                end_date=week_data["end_date"]
            )
            for week_id, week_data in weeks_db.items()
        ]
        
        return weeks
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la récupération des semaines: {str(e)}"
        )

@router.get("/data/{week_id}", response_model=KpiData)
async def get_kpi_data_by_week(week_id: str, current_user: User = Depends(get_current_user)):
    """Récupérer les données KPI pour une semaine spécifique"""
    # Vérifier si la semaine existe
    if week_id not in weeks_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Semaine non trouvée"
        )
    
    # Récupérer les données KPI pour cette semaine
    kpi_data = next((data for data in kpi_data_db.values() if data["week_id"] == week_id), None)
    
    if not kpi_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Données KPI non trouvées pour cette semaine"
        )
    
    try:
        # Convertir la chaîne ISO en objet datetime
        timestamp = datetime.fromisoformat(kpi_data["timestamp"])
        
        return KpiData(
            id=kpi_data["id"],
            week_id=kpi_data["week_id"],
            timestamp=timestamp,
            kpis=kpi_data["kpis"],
            top_partners=kpi_data["top_partners"],
            service_quality=kpi_data["service_quality"]
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la récupération des données KPI: {str(e)}"
        ) 

# Nouvelles routes pour la table SteeringOfRoaming
# @router.get("/success_rate")
# async def success_rate_endpoint(country: Optional[str] = None, operator: Optional[str] = None):
#     """Retourne le taux de réussite par pays avec filtrage optionnel"""
#     try:
#         result = calculate_success_rate(country=country)
#         # Si un opérateur est spécifié, filtrer les résultats par opérateur
#         if operator:
#             df = get_steering_data(country=country, operator=operator)
#             success_rate = df.groupby('a_locationContry')['verdict'].value_counts(normalize=True).unstack()
#             success_rate = success_rate.fillna(0)
#             
#             # Assurer que les colonnes 'Success' et 'Fail' existent
#             if 'Success' not in success_rate.columns:
#                 success_rate['Success'] = 0
#             if 'Fail' not in success_rate.columns:
#                 success_rate['Fail'] = 0
#             
#             success_rate['Success'] = success_rate['Success'] * 100
#             success_rate['Fail'] = success_rate['Fail'] * 100
#             
#             # Ajouter le nombre total d'entrées par pays
#             entry_counts = df.groupby('a_locationContry').size()
#             success_rate_dict = success_rate.to_dict('index')
#             
#             # Ajouter le comptage au dictionnaire
#             for country in success_rate_dict:
#                 success_rate_dict[country]['total_entries'] = int(entry_counts[country])
#                 success_rate_dict[country]['operator'] = operator
#             
#             return dict(sorted(success_rate_dict.items(), key=lambda x: x[1]['Success'], reverse=True))
#         return result
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))

@router.get("/fail_causes")
async def fail_causes_endpoint(country: Optional[str] = None, operator: Optional[str] = None, verdict: Optional[str] = None):
    """
    Retourne les causes d'échec pour un pays et/ou un opérateur spécifique.
    
    Args:
        country (str, optional): Le pays pour filtrer les données
        operator (str, optional): L'opérateur pour filtrer les données
        verdict (str, optional): Le verdict pour filtrer les données (par défaut 'FAIL')
    
    Returns:
        dict: Un dictionnaire contenant les causes d'échec, les PLMN rejetés et les top échecs
    """
    try:
        # Utiliser Kpi_service_clean.py pour récupérer les données
        from ..Kpi_service_clean import get_fail_causes
        result = get_fail_causes(country=country, operator=operator)
        return result
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/weekly_attach_trend")
async def weekly_attach_trend_endpoint(country: Optional[str] = None, operator: Optional[str] = None):
    """Retourne les tendances hebdomadaires des attaches avec filtrage optionnel"""
    try:
        if operator:
            df = get_steering_data(country=country, operator=operator)
            
            if df.empty:
                return []
            
            # Assurer que ces colonnes existent
            for col in ['attache0 %', 'attache1 %', 'attache2 %']:
                if col not in df.columns:
                    df[col] = 0
            
            weekly_stats = df.groupby(['weekofyear', 'a_locationContry']).agg({
                'a_LupDuration': 'mean',
                'attache0 %': 'mean',
                'attache1 %': 'mean',
                'attache2 %': 'mean',
                'verdict': lambda x: (x == 'Success').mean() * 100  # Pourcentage de succès
            }).reset_index()
            
            # Renommer pour clarté
            weekly_stats = weekly_stats.rename(columns={
                'a_LupDuration': 'avg_duration',
                'verdict': 'success_rate'
            })
            
            # Ajouter l'opérateur pour référence
            weekly_stats['operator'] = operator
            
            # Arrondir les valeurs pour lisibilité
            numeric_cols = ['avg_duration', 'attache0 %', 'attache1 %', 'attache2 %', 'success_rate']
            weekly_stats[numeric_cols] = weekly_stats[numeric_cols].round(2)
            
            return weekly_stats.to_dict('records')
        else:
            return get_weekly_attach_trend(country=country)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/annual_trend")
async def annual_trend_endpoint(country: Optional[str] = None, operator: Optional[str] = None):
    """Retourne les tendances annuelles avec filtrage optionnel"""
    try:
        if operator:
            df = get_steering_data(country=country, operator=operator)
            
            if df.empty:
                return {}
            
            # Créer une colonne pour le mois à partir du timestamp
            df['month'] = pd.to_datetime(df['timestamp']).dt.month
            
            # Grouper par mois et calculer les métriques
            monthly_stats = df.groupby('month').agg({
                'verdict': lambda x: (x == 'Success').mean() * 100,
                'a_LupDuration': 'mean',
                'attache0 %': 'mean',
                'attache1 %': 'mean',
                'attache2 %': 'mean'
            }).reset_index()
            
            # Renommer pour clarté
            monthly_stats = monthly_stats.rename(columns={
                'verdict': 'success_rate',
                'a_LupDuration': 'avg_duration'
            })
            
            # Ajouter l'opérateur
            monthly_stats['operator'] = operator
            
            # Arrondir les valeurs 
            numeric_cols = ['success_rate', 'avg_duration', 'attache0 %', 'attache1 %', 'attache2 %']
            monthly_stats[numeric_cols] = monthly_stats[numeric_cols].round(2)
            
            # Ajouter les noms des mois pour faciliter l'affichage
            month_names = {
                1: 'Janvier', 2: 'Février', 3: 'Mars', 4: 'Avril', 5: 'Mai', 6: 'Juin',
                7: 'Juillet', 8: 'Août', 9: 'Septembre', 10: 'Octobre', 11: 'Novembre', 12: 'Décembre'
            }
            
            monthly_stats['month_name'] = monthly_stats['month'].map(month_names)
            
            return monthly_stats.to_dict('records')
        else:
            return get_annual_trend(country=country)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/failure_analysis")
async def failure_analysis_endpoint(country: Optional[str] = None, operator: Optional[str] = None):
    """Retourne l'analyse détaillée des échecs avec filtrage optionnel"""
    try:
        if operator:
            df = get_steering_data(filter_verdict='Fail', country=country, operator=operator)
            
            if df.empty:
                return {
                    "by_network_type": {},
                    "by_country": {},
                    "by_day": {},
                    "attachment_stats": {}
                }
            
            # Analyse par type de réseau
            network_analysis = df.groupby('a_networkType').size().to_dict()
            
            # Analyse par pays 
            country_analysis = df.groupby('a_locationContry').size().to_dict()
            
            # Analyse par jour de la semaine
            day_mapping = {
                0: 'Lundi', 1: 'Mardi', 2: 'Mercredi', 3: 'Jeudi', 
                4: 'Vendredi', 5: 'Samedi', 6: 'Dimanche'
            }
            df['day_name'] = df['dayofweek'].map(day_mapping)
            day_analysis = df.groupby('day_name').size().to_dict()
            
            # Utiliser les données de l'endpoint attachment_rates_by_country pour avoir tous les niveaux d'attachement (0-6)
            attachment_data = get_attachment_rates_by_country(country=country, operator=operator)
            
            # Initialiser les statistiques d'attachement avec tous les niveaux (0-6)
            attachment_stats = {}
            
            if attachment_data and 'data' in attachment_data and attachment_data['data']:
                # Calculer la moyenne des taux d'attachement pour chaque niveau
                for i in range(7):  # Niveaux 0 à 6
                    attachment_key = f'attache{i}'
                    attachment_values = []
                    
                    for item in attachment_data['data']:
                        if 'attachment_distribution' in item and str(i) in item['attachment_distribution']:
                            attachment_values.append(item['attachment_distribution'][str(i)])
                    
                    if attachment_values:
                        attachment_stats[attachment_key] = sum(attachment_values) / len(attachment_values)
                    else:
                        attachment_stats[attachment_key] = 0.0
            else:
                # Fallback aux anciennes statistiques si les nouvelles ne sont pas disponibles
                attachment_stats = {
                    'attache0': df['attache0 %'].mean() if 'attache0 %' in df.columns else 0.0,
                    'attache1': df['attache1 %'].mean() if 'attache1 %' in df.columns else 0.0,
                    'attache2': df['attache2 %'].mean() if 'attache2 %' in df.columns else 0.0,
                    'attache3': 0.0,
                    'attache4': 0.0,
                    'attache5': 0.0,
                    'attache6': 0.0
                }
            
            # Récupérer les taux de succès par pays
            success_data = get_steering_success_by_country(country=country, operator=operator)
            success_rates = {}
            
            if success_data:
                for item in success_data:
                    if 'a_locationContry' in item and 'success_rate' in item:
                        success_rates[item['a_locationContry']] = item['success_rate']
            
            return {
                "by_network_type": network_analysis,
                "by_country": country_analysis,
                "by_day": day_analysis,
                "attachment_stats": attachment_stats,
                "success_rates": success_rates,
                "operator": operator
            }
        else:
            # Utiliser les données des autres endpoints pour enrichir l'analyse
            base_analysis = get_failure_analysis_by_filter(country=country)
            
            # Enrichir avec les données d'attachement complètes
            attachment_data = get_attachment_rates_by_country(country=country)
            
            if attachment_data and 'data' in attachment_data and attachment_data['data']:
                # Calculer la moyenne des taux d'attachement pour chaque niveau
                attachment_stats = {}
                for i in range(7):  # Niveaux 0 à 6
                    attachment_key = f'attache{i}'
                    attachment_values = []
                    
                    for item in attachment_data['data']:
                        if 'attachment_distribution' in item and str(i) in item['attachment_distribution']:
                            attachment_values.append(item['attachment_distribution'][str(i)])
                    
                    if attachment_values:
                        attachment_stats[attachment_key] = sum(attachment_values) / len(attachment_values)
                    else:
                        attachment_stats[attachment_key] = 0.0
                
                # Remplacer les statistiques d'attachement dans l'analyse de base
                base_analysis['attachment_stats'] = attachment_stats
            
            # Ajouter les taux de succès par pays
            success_data = get_steering_success_by_country(country=country)
            success_rates = {}
            
            if success_data:
                for item in success_data:
                    if 'a_locationContry' in item and 'success_rate' in item:
                        success_rates[item['a_locationContry']] = item['success_rate']
                
                base_analysis['success_rates'] = success_rates
            
            return base_analysis
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/country_overview")
async def country_overview_endpoint(operator: Optional[str] = None):
    """Retourne un aperçu des performances par pays"""
    try:
        if operator:
            df = get_steering_data(operator=operator)
            
            if df.empty:
                return []
            
            # Grouper par pays et calculer les statistiques
            country_stats = df.groupby('a_locationContry').agg({
                'verdict': lambda x: (x == 'Success').mean() * 100,
                'a_LupDuration': 'mean',
                'attache0 %': 'mean',
                'attache1 %': 'mean',
                'attache2 %': 'mean',
                'a_nrofluprequests': 'mean'
            }).reset_index()
            
            # Compter le nombre d'entrées par pays
            country_counts = df.groupby('a_locationContry').size().to_dict()
            
            # Renommer pour clarté
            country_stats = country_stats.rename(columns={
                'verdict': 'success_rate',
                'a_LupDuration': 'avg_duration',
                'a_nrofluprequests': 'avg_requests'
            })
            
            # Ajouter le comptage au DataFrame
            country_stats['count'] = country_stats['a_locationContry'].map(country_counts)
            country_stats['operator'] = operator
            
            # Arrondir les valeurs numériques
            numeric_cols = ['success_rate', 'avg_duration', 'attache0 %', 'attache1 %', 'attache2 %', 'avg_requests']
            country_stats[numeric_cols] = country_stats[numeric_cols].round(2)
            
            return country_stats.to_dict('records')
        else:
            return get_country_overview()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/steering_success_by_country")
async def steering_success_by_country_endpoint(
    country: Optional[str] = None, 
    operator: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """Renvoie les données de taux de réussite par pays pour le graphique de steering avec filtrage optionnel par date"""
    try:
        data = get_steering_success_by_country(country=country, operator=operator, start_date=start_date, end_date=end_date)
        print(f"Données récupérées pour steering_success_by_country: {data}")
        
        # Vérifier le format des données
        if not data:
            print("Aucune donnée récupérée pour steering_success_by_country")
            # Générer des données fictives pour éviter une erreur côté frontend
            countries = ["France", "Espagne", "Allemagne", "Italie", "Royaume-Uni", 
                        "Belgique", "Pays-Bas", "Suisse", "Portugal", "Maroc"]
            
            import random
            data = []
            for country_name in countries:
                # Générer un taux de succès aléatoire entre 70 et 98%
                rate = round(random.uniform(70, 98), 1)
                data.append({
                    "a_locationContry": country_name,
                    "success_rate": rate
                })
        
        # S'assurer que le format est celui attendu par le frontend
        for item in data:
            if 'a_locationContry' not in item:
                if 'country' in item:
                    item['a_locationContry'] = item['country']
                else:
                    item['a_locationContry'] = "Unknown"
                    
            if 'success_rate' not in item:
                if 'rate' in item:
                    item['success_rate'] = item['rate']
                else:
                    item['success_rate'] = 50.0  # Valeur par défaut
        
        # Filtrer les données par pays si le paramètre est fourni
        if country and country != 'all':
            data = [item for item in data if item['a_locationContry'] == country or item['country'] == country]
        
        # Filtrer les données par opérateur si le paramètre est fourni
        if operator and operator != 'all':
            # Comme les données n'ont pas d'opérateur par défaut, on utilise les données de l'API steering_data
            # pour récupérer les taux de succès par pays et opérateur
            df = get_steering_data(country=country, operator=operator)
            if not df.empty:
                # Calculer le taux de succès par pays pour cet opérateur
                success_rates = df.groupby('a_location_country').apply(
                    lambda x: (x['Verdict'].str.lower() == 'pass').mean() * 100
                ).reset_index()
                success_rates.columns = ['a_locationContry', 'success_rate']
                
                # Remplacer les données existantes
                data = success_rates.to_dict('records')
        
        return data
    except Exception as e:
        print(f"Erreur dans steering_success_by_country_endpoint: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/attachment_rates_by_country")
async def attachment_rates_by_country_endpoint(
    country: Optional[str] = None, 
    operator: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """Renvoie les taux d'attachement par pays pour le graphique de steering avec filtrage optionnel"""
    try:
        data = get_attachment_rates_by_country(country=country, operator=operator, start_date=start_date, end_date=end_date)
        print(f"Données d'attachement brutes: {data[:2]}...")  # Affiche les 2 premiers éléments
        
        # Vérifier si nous avons des données
        if not data:
            print("ATTENTION: Aucune donnée d'attachement récupérée")
            return []
        
        # S'assurer que chaque élément a le bon format pour le frontend
        for item in data:
            # S'assurer que les clés sont cohérentes
            keys_to_check = ['attache0 %', 'attache1 %', 'attache2 %', 'attache3 %', 
                            'attache4 %', 'attache5 %', 'attache6 %', 'attach_success_rate']
            
            for key in keys_to_check:
                if key not in item:
                    if key == 'attach_success_rate':
                        item[key] = 50.0  # Valeur par défaut
                    else:
                        item[key] = 0.0  # Valeur par défaut
            
            # Vérifier que la somme des taux d'attachement est proche de 100%
            attach_sum = sum([item[f'attache{i} %'] for i in range(7)])
            if abs(attach_sum - 100) > 1.0:  # Si plus de 1% d'écart
                factor = 100 / attach_sum if attach_sum > 0 else 0
                for i in range(7):
                    item[f'attache{i} %'] = round(item[f'attache{i} %'] * factor, 1)
            
            # Ajouter les statistiques de réseau avec la répartition des connexions
            item['network_stats'] = {
                '2G': {
                    'connection_percentage': item['attache0 %'] + item['attache1 %'],
                    'success_rate': 90.0  # Taux de succès fictif pour 2G
                },
                '3G': {
                    'connection_percentage': item['attache2 %'] + item['attache3 %'],
                    'success_rate': 95.0  # Taux de succès fictif pour 3G
                },
                '4G': {
                    'connection_percentage': item['attache4 %'] + item['attache5 %'],
                    'success_rate': 98.0  # Taux de succès fictif pour 4G
                },
                '5G': {
                    'connection_percentage': item['attache6 %'],
                    'success_rate': 99.0  # Taux de succès fictif pour 5G
                }
            }
        
        # Filtrer les données par pays si le paramètre est fourni
        if country and country != 'all':
            data = [item for item in data if item['country'] == country]
        
        # Filtrer les données par opérateur si le paramètre est fourni
        if operator and operator != 'all':
            # Comme les données n'ont pas d'opérateur par défaut, on utilise les données de l'API steering_data
            # pour récupérer les taux d'attachement par pays et opérateur
            df = get_steering_data(country=country, operator=operator)
            if not df.empty:
                # Calculer les taux de connexion par type de réseau
                network_stats = {}
                
                # Colonnes pour chaque type de réseau (à adapter selon votre schéma de données)
                network_columns = {
                    '2G': ['a_2G', 'attache0 %', 'attache1 %'],
                    '3G': ['a_3G', 'attache2 %', 'attache3 %'],
                    '4G': ['a_4G', 'attache4 %', 'attache5 %'],
                    '5G': ['a_5G', 'attache6 %']
                }
                
                # Calculer la répartition des connexions par pays et par type de réseau
                country_connection_stats = {}
                
                # Grouper par pays
                for country_name, group in df.groupby('a_location_country'):
                    if country_name not in country_connection_stats:
                        country_connection_stats[country_name] = {
                            '2G': 0,
                            '3G': 0,
                            '4G': 0,
                            '5G': 0
                        }
                    
                    # Compter le nombre total de connexions pour ce pays
                    total_connections = len(group)
                    
                    # Compter les connexions par type de réseau
                    for net_type, columns in network_columns.items():
                        # Essayer différentes colonnes possibles
                        for col in columns:
                            if col in group.columns:
                                # Compter les connexions où cette colonne a une valeur positive
                                connections = group[group[col] > 0].shape[0]
                                country_connection_stats[country_name][net_type] += connections
                                break
                    
                    # Calculer les pourcentages
                    if total_connections > 0:
                        for net_type in country_connection_stats[country_name]:
                            country_connection_stats[country_name][net_type] = (
                                country_connection_stats[country_name][net_type] / total_connections * 100
                            )
                
                # Formater les données pour correspondre à la structure attendue
                filtered_data = []
                for country_name, stats in country_connection_stats.items():
                    # S'assurer que les pourcentages totalisent 100%
                    total_percentage = sum(stats.values())
                    if total_percentage > 0:
                        factor = 100 / total_percentage
                        for net_type in stats:
                            stats[net_type] *= factor
                    
                    item = {
                        'country': country_name,
                        'overall_success_rate': 95.0,  # Taux de succès global fictif
                        'network_stats': {
                            net_type: {
                                'connection_percentage': round(percentage, 1),
                                'success_rate': 95.0 + (net_type == '4G') * 3.0  # Taux de succès fictif plus élevé pour 4G
                            }
                            for net_type, percentage in stats.items() if percentage > 0
                        }
                    }
                    filtered_data.append(item)
                
                # Remplacer les données existantes
                if filtered_data:
                    data = filtered_data
        
        print(f"Données d'attachement formatées envoyées: {len(data)} pays")
        return data
    except Exception as e:
        print(f"ERREUR dans attachment_rates_by_country_endpoint: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e)) 

# Assurez-vous que DB_CONFIG est accessible ici ou importez-le si défini ailleurs
# Pour l'instant, nous allons le définir ici temporairement en utilisant la structure connue:
DB_CONFIG = {
    "host": "localhost",
    "user": "root",
    "password": "",  # Remplacez par votre mot de passe réel
    "db": "Kpi",  # Votre base de données
    "charset": "utf8mb4",
    "cursorclass": pymysql.cursors.DictCursor
}

def get_db_connection():
    """Établir une connexion à la base de données"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        return connection
    except Exception as e:
        # Log l'erreur pour le débogage côté serveur
        print(f"Erreur de connexion à la base de données: {e}")
        # Renvoyer None ou lever une exception gérée par l'endpoint
        return None # Nous allons gérer None dans l'endpoint

# Définir un modèle Pydantic pour la réponse de l'endpoint /stats
from pydantic import BaseModel

class AttachmentStats(BaseModel):
    a_location_country: str
    a_UsedPLMNName: str
    total_attempts: int
    successful_attempts: int
    attachment_success_rate: float # ou Decimal si plus de précision est nécessaire

# Nouvel endpoint /stats
@router.get("/stats", response_model=List[AttachmentStats])
async def get_attachment_stats():
    """Retourne le taux de succès d'attachement par pays et opérateur après nettoyage.

    Effectue le nettoyage de base (lignes vides, doublons exacts) et calcule les statistiques.
    """
    conn = None
    try:
        conn = get_db_connection()
        if conn is None:
             # Si la connexion échoue, lever une HTTPException 500
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Impossible de se connecter à la base de données."
            )

        # Charger toutes les données de la table
        query = "SELECT * FROM steeringofroaming"
        df = pd.read_sql(query, conn)
        
        # --- Étapes de Nettoyage ---
        # Remplacer les chaînes vides par NaN pour un nettoyage facile
        df.replace('', pd.NA, inplace=True)

        # Supprimer les lignes avec au moins une valeur manquante ou vide
        # Utiliser how='any' pour supprimer la ligne si n'importe quelle colonne a un NaN
        df_cleaned_empty = df.dropna(how='any')

        # Supprimer les doublons exacts (basé sur toutes les colonnes)
        df_cleaned = df_cleaned_empty.drop_duplicates()

        # --- Calcul des statistiques ---
        required_cols = ['a_location_country', 'a_UsedPLMNName', 'a_NrOfLupRequests', 'Success']
        if not all(col in df_cleaned.columns for col in required_cols):
            missing = [col for col in required_cols if col not in df_cleaned.columns]
            # Si des colonnes nécessaires manquent, lever une HTTPException 500
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Colonnes de données nécessaires manquantes après nettoyage: {missing}."
            )

        # Convertir a_NrOfLupRequests et Success en numérique
        df_cleaned['a_NrOfLupRequests'] = pd.to_numeric(df_cleaned['a_NrOfLupRequests'], errors='coerce').fillna(0).astype(int)
        df_cleaned['Success_binary'] = pd.to_numeric(df_cleaned['Success'], errors='coerce').fillna(0).astype(int)

        # Filtrer les lignes où total_attempts est 0 avant le regroupement pour éviter la division par zéro
        df_calculable = df_cleaned[df_cleaned['a_NrOfLupRequests'] > 0].copy()

        # Calculer les statistiques par groupe
        stats = df_calculable.groupby(['a_location_country', 'a_UsedPLMNName']).agg(
            total_attempts=('a_NrOfLupRequests', 'sum'),
            successful_attempts=('Success_binary', 'sum')
        ).reset_index()
        
        # Calculer le taux de succès d'attachement (%)
        stats['attachment_success_rate'] = (stats['successful_attempts'] / stats['total_attempts']).replace([float('inf'), -float('inf')], pd.NA).fillna(0) * 100

        # Trier les résultats par pays puis opérateur
        stats_sorted = stats.sort_values(by=['a_location_country', 'a_UsedPLMNName'])

        # Convertir le DataFrame pandas en liste de dictionnaires pour la réponse JSON
        return stats_sorted.to_dict(orient='records')
        
    except Exception as e:
        # Capter toute autre exception et retourner une HTTPException 500
        print(f"Erreur lors du traitement des données de steering: {e}") # Log côté serveur
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur interne du serveur lors du traitement des données: {str(e)}"
        )

    finally:
        if conn:
            conn.close() 

@router.get("/failure_details")
async def failure_details_endpoint(verdict: Optional[str] = None):
    """Retourne les détails des échecs avec filtrage optionnel par verdict"""
    try:
        # Utiliser Kpi_service_clean.py pour récupérer les détails
        from ..Kpi_service_clean import get_failure_details
        result = get_failure_details(verdict=verdict)
        return {"data": result}
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/top_data_consumption_countries")
async def top_data_consumption_countries_endpoint():
    """
    Retourne les 20 pays qui consomment le plus de données en roaming
    avec leur consommation de données, nombre de connexions,
    et pourcentage de changement par rapport à la période précédente.
    """
    try:
        # Utiliser Kpi_service_clean.py pour récupérer les données
        from ..Kpi_service_clean import get_top_data_consumption_countries
        result = get_top_data_consumption_countries()
        return {"data": result}
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e)) 

@router.post("/kpi/train")
async def train_ml_model(
    days_back: int = 30,
    current_user: User = Depends(get_current_user)
):
    """Entraîne le modèle ML avec les données historiques de la base de données"""
    try:
        result = ml_service.train_model(days_back)
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de l'entraînement du modèle: {str(e)}"
        )

@router.post("/kpi/analyze")
async def analyze_kpis(
    days_to_analyze: int = 1,
    thresholds: Optional[Dict[str, float]] = None,
    current_user: User = Depends(get_current_user)
):
    """Analyse les KPIs avec le modèle ML"""
    try:
        results = ml_service.analyze_kpis(days_to_analyze, thresholds)
        
        return {
            "timestamp": datetime.now(),
            "analysis_results": results,
            "total_anomalies": len(results),
            "analyzed_period": f"Derniers {days_to_analyze} jours"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de l'analyse des KPIs: {str(e)}"
        )

# Initialisation du détecteur d'anomalies
kpi_detector = KPIAnomalyDetector()

@router.post("/train")
async def train_model(
    file: Optional[UploadFile] = File(None),
    existing_file: Optional[str] = Form(None),
    test_split: float = Form(0.2)
):
    """
    Entraîne le modèle de détection d'anomalies avec soit:
    - Un fichier CSV uploadé
    - Un fichier CSV existant dans le système
    
    Args:
        file: Fichier CSV uploadé
        existing_file: Chemin vers un fichier CSV existant
        test_split: Proportion des données à utiliser pour le test (0.2 = 20%)
    """
    try:
        training_summary = {
            "status": "success",
            "timestamp": datetime.now().isoformat(),
            "data_source": "uploaded_file" if file else "existing_file",
            "training_details": {}
        }

        # Chargement des données
        if file:
            # Lecture du fichier uploadé
            content = await file.read()
            with open(f"uploads/{file.filename}", "wb") as f:
                f.write(content)
            df = pd.read_csv(f"uploads/{file.filename}")
            training_summary["file_name"] = file.filename
        elif existing_file:
            # Vérification que le fichier existe
            if not os.path.exists(existing_file):
                raise HTTPException(
                    status_code=404,
                    detail=f"Le fichier {existing_file} n'existe pas"
                )
            df = pd.read_csv(existing_file)
            training_summary["file_name"] = os.path.basename(existing_file)
        else:
            raise HTTPException(
                status_code=400,
                detail="Aucune source de données fournie"
            )

        # Préparation des données
        # Conversion des colonnes de date si elles existent
        if "timestamp" in df.columns:
            df["timestamp"] = pd.to_datetime(df["timestamp"])
            df = df.sort_values("timestamp")

        # Statistiques sur les données
        training_summary["training_details"].update({
            "total_rows": len(df),
            "columns": list(df.columns),
            "numeric_columns": list(df.select_dtypes(include=['float64', 'int64']).columns),
            "date_range": {
                "start": df["timestamp"].min().isoformat() if "timestamp" in df.columns else None,
                "end": df["timestamp"].max().isoformat() if "timestamp" in df.columns else None
            }
        })

        # Entraînement du modèle
        logger.info(f"Début de l'entraînement avec {len(df)} lignes")
        kpi_detector.train(df)
        
        # Évaluation sur un échantillon de test
        if len(df) > 100:  # Si assez de données pour faire une évaluation
            test_data = df.sample(n=min(1000, int(len(df) * test_split)))
            anomalies = kpi_detector.predict_anomalies(test_data)
            
            training_summary["training_details"].update({
                "evaluation": {
                    "test_sample_size": len(test_data),
                    "anomalies_detected": len(anomalies["anomalies"]),
                    "detection_rate": len(anomalies["anomalies"]) / len(test_data),
                    "methods_summary": anomalies["methods_summary"]
                }
            })

        # Sauvegarde du résumé d'entraînement
        summary_path = os.path.join("models", "training_summary.json")
        with open(summary_path, "w") as f:
            json.dump(training_summary, f, indent=2)

        return training_summary

    except Exception as e:
        logger.error(f"Erreur lors de l'entraînement: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de l'entraînement: {str(e)}"
        )

@router.get("/kpi/alerts", response_model=Dict[str, List[Dict[str, Any]]])
async def get_kpi_alerts(current_user: User = Depends(get_current_user)):
    """
    Récupère les alertes KPI pour tous les pays
    """
    try:
        alerts = kpi_alerts_service.get_country_alerts()
        return {"anomalies": alerts}
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des alertes KPI: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la récupération des alertes KPI: {str(e)}"
        )

# Importer les fonctions du service SteeringOfRoaming