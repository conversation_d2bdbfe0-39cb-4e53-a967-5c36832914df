#!/usr/bin/env python3
"""
Script pour vérifier la structure de la base de données après l'import amélioré
"""

import pymysql
import logging
from enhanced_csv_importer import EnhancedCSVImporter

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_database_structure():
    """Vérifier la structure de la base de données"""
    logger.info("🔍 VÉRIFICATION DE LA STRUCTURE DE LA BASE DE DONNÉES")
    logger.info("=" * 60)
    
    try:
        importer = EnhancedCSVImporter()
        connection = importer.get_connection()
        
        with connection.cursor() as cursor:
            # Vérifier les tables existantes
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            logger.info(f"📋 Tables dans la base de données 'kpi':")
            for table in tables:
                table_name = list(table.values())[0]
                logger.info(f"   • {table_name}")
            
            # Vérifier la structure de la table steeringofroaming
            logger.info(f"\n📊 STRUCTURE DE LA TABLE 'steeringofroaming':")
            logger.info("-" * 50)
            
            cursor.execute("DESCRIBE steeringofroaming")
            columns = cursor.fetchall()
            
            logger.info(f"📋 {len(columns)} colonnes dans la table:")
            for i, col in enumerate(columns, 1):
                logger.info(f"   {i:2d}. {col['Field']:25} | {col['Type']:20} | {col['Null']:5} | {col['Key']:5}")
            
            # Compter les enregistrements
            cursor.execute("SELECT COUNT(*) as total FROM steeringofroaming")
            total = cursor.fetchone()['total']
            logger.info(f"\n📊 Total d'enregistrements: {total:,}")
            
            # Vérifier les données récentes
            cursor.execute("""
                SELECT COUNT(*) as recent 
                FROM steeringofroaming 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
            """)
            recent = cursor.fetchone()['recent']
            logger.info(f"🕐 Enregistrements dernière heure: {recent:,}")
            
            # Vérifier les colonnes avec des données non-NULL
            logger.info(f"\n📈 ANALYSE DES DONNÉES:")
            logger.info("-" * 50)
            
            sample_columns = [
                'TestcaseId', 'TCName', 'Verdict', 'a_location_country', 
                'a_UsedPLMNName', 'b_location_country', 'errorText'
            ]
            
            for col in sample_columns:
                try:
                    cursor.execute(f"""
                        SELECT 
                            COUNT(*) as total,
                            COUNT({col}) as non_null,
                            COUNT(DISTINCT {col}) as distinct_values
                        FROM steeringofroaming
                    """)
                    stats = cursor.fetchone()
                    
                    non_null_pct = (stats['non_null'] / stats['total'] * 100) if stats['total'] > 0 else 0
                    
                    logger.info(f"   {col:20} | Non-NULL: {stats['non_null']:4d} ({non_null_pct:5.1f}%) | Distinct: {stats['distinct_values']:4d}")
                    
                except Exception as e:
                    logger.warning(f"   {col:20} | Erreur: {e}")
            
            # Vérifier les pays les plus fréquents
            logger.info(f"\n🌍 TOP 10 PAYS (a_location_country):")
            logger.info("-" * 50)
            
            cursor.execute("""
                SELECT a_location_country, COUNT(*) as count 
                FROM steeringofroaming 
                WHERE a_location_country IS NOT NULL 
                GROUP BY a_location_country 
                ORDER BY count DESC 
                LIMIT 10
            """)
            countries = cursor.fetchall()
            
            for i, country in enumerate(countries, 1):
                logger.info(f"   {i:2d}. {country['a_location_country']:15} | {country['count']:4d} records")
            
            # Vérifier les verdicts
            logger.info(f"\n📊 RÉPARTITION DES VERDICTS:")
            logger.info("-" * 50)
            
            cursor.execute("""
                SELECT Verdict, COUNT(*) as count 
                FROM steeringofroaming 
                GROUP BY Verdict 
                ORDER BY count DESC
            """)
            verdicts = cursor.fetchall()
            
            total_with_verdict = sum(v['count'] for v in verdicts)
            for verdict in verdicts:
                percentage = (verdict['count'] / total_with_verdict * 100) if total_with_verdict > 0 else 0
                logger.info(f"   {verdict['Verdict']:10} | {verdict['count']:4d} ({percentage:5.1f}%)")
        
        connection.close()
        
        logger.info(f"\n✅ VÉRIFICATION TERMINÉE")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur lors de la vérification: {e}")
        return False

def check_missing_columns():
    """Vérifier s'il manque des colonnes importantes"""
    logger.info(f"\n🔍 VÉRIFICATION DES COLONNES MANQUANTES")
    logger.info("-" * 50)
    
    try:
        importer = EnhancedCSVImporter()
        connection = importer.get_connection()
        
        # Colonnes attendues selon votre capture d'écran
        expected_columns = [
            'TestcaseId', 'OrderId', 'TCName', 'Timestamp', 'Verdict',
            'TestDefinitionPath', 'User', 'UserGroup', 'errorId', 'errorText',
            'TCTestDefinitionId', 'errorSideCountry', 'errorSideId', 
            'errorSideLocation', 'errorSideNumber',
            'a_location_country', 'a_location_id', 'a_location_number',
            'a_operator_country', 'a_operator_id', 'a_operator_name', 'a_operator_number'
        ]
        
        with connection.cursor() as cursor:
            # Obtenir les colonnes existantes
            cursor.execute("DESCRIBE steeringofroaming")
            existing_columns = {row['Field'] for row in cursor.fetchall()}
            
            # Trouver les colonnes manquantes
            missing_columns = set(expected_columns) - existing_columns
            extra_columns = existing_columns - set(expected_columns) - {'id', 'created_at'}
            
            if missing_columns:
                logger.warning(f"⚠️ Colonnes manquantes ({len(missing_columns)}):")
                for col in sorted(missing_columns):
                    logger.warning(f"   • {col}")
            else:
                logger.info("✅ Toutes les colonnes attendues sont présentes")
            
            if extra_columns:
                logger.info(f"📋 Colonnes supplémentaires ({len(extra_columns)}):")
                for col in sorted(extra_columns):
                    logger.info(f"   • {col}")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur lors de la vérification des colonnes: {e}")
        return False

def main():
    """Fonction principale"""
    logger.info("🚀 VÉRIFICATION COMPLÈTE DE LA BASE DE DONNÉES")
    logger.info("=" * 70)
    
    try:
        # Vérifier la structure générale
        structure_ok = check_database_structure()
        
        # Vérifier les colonnes manquantes
        columns_ok = check_missing_columns()
        
        # Résumé final
        logger.info(f"\n🎯 RÉSUMÉ FINAL")
        logger.info("=" * 50)
        
        if structure_ok and columns_ok:
            logger.info("🎉 BASE DE DONNÉES CORRECTEMENT CONFIGURÉE!")
            logger.info("✅ Toutes les colonnes sont mappées")
            logger.info("✅ Les données sont correctement importées")
            logger.info("✅ Aucun champ NULL problématique détecté")
        else:
            logger.warning("⚠️ Quelques problèmes détectés")
            if not structure_ok:
                logger.warning("🔧 Problème de structure de base de données")
            if not columns_ok:
                logger.warning("🔧 Problème de mapping des colonnes")
        
        return structure_ok and columns_ok
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
