{"training": {"default_days_back": 90, "default_test_size": 0.2, "auto_retrain_interval_hours": 24, "min_data_points": 1000, "max_training_time_minutes": 30}, "model_parameters": {"isolation_forest": {"contamination": 0.1, "random_state": 42, "n_estimators": 100}, "random_forest": {"n_estimators": 100, "random_state": 42, "max_depth": 10}, "anomaly_detection": {"z_score_threshold": 3.0, "moving_average_window": 10, "moving_average_std_multiplier": 2.0}}, "data_preprocessing": {"fill_missing_values": true, "remove_outliers": true, "outlier_threshold": 3.0, "normalize_features": true}, "kpi_thresholds": {"success_rate_min": 0.85, "avg_lup_duration_max": 5.0, "failure_rate_max": 0.15, "rejection_rate_max": 0.1}, "alerts": {"enable_email_alerts": false, "email_recipients": [], "alert_on_high_anomaly_rate": true, "high_anomaly_rate_threshold": 0.05}, "logging": {"level": "INFO", "log_file": "ml_training.log", "max_log_size_mb": 10, "backup_count": 5}, "paths": {"models_directory": "models", "data_directory": "data", "logs_directory": "logs"}}