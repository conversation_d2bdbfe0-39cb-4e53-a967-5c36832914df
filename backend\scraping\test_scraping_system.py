#!/usr/bin/env python3
"""
Test complet du système de scraping automatique
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_environment():
    """Tester l'environnement et les dépendances"""
    logger.info("🧪 TEST DE L'ENVIRONNEMENT")
    logger.info("=" * 50)
    
    # Vérifier Python
    logger.info(f"Python version: {sys.version}")
    
    # Vérifier les dépendances
    dependencies = [
        'selenium', 'pandas', 'pymysql', 'schedule', 
        'requests', 'python-dotenv'
    ]
    
    missing_deps = []
    for dep in dependencies:
        try:
            __import__(dep.replace('-', '_'))
            logger.info(f"✅ {dep} - OK")
        except ImportError:
            logger.error(f"❌ {dep} - MANQUANT")
            missing_deps.append(dep)
    
    if missing_deps:
        logger.error(f"Dépendances manquantes: {missing_deps}")
        return False
    
    return True

def test_database_connection():
    """Tester la connexion à la base de données"""
    logger.info("\n🗄️ TEST DE LA BASE DE DONNÉES")
    logger.info("=" * 50)
    
    try:
        from csv_importer import CSVImporter
        
        importer = CSVImporter()
        connection = importer.get_connection()
        
        with connection.cursor() as cursor:
            # Vérifier la table
            cursor.execute("SHOW TABLES LIKE 'steeringofroaming'")
            table_exists = cursor.fetchone()
            
            if table_exists:
                logger.info("✅ Table steeringofroaming existe")
                
                # Compter les enregistrements
                cursor.execute("SELECT COUNT(*) as count FROM steeringofroaming")
                count = cursor.fetchone()['count']
                logger.info(f"📊 Nombre d'enregistrements: {count}")
                
                return True
            else:
                logger.error("❌ Table steeringofroaming n'existe pas")
                return False
                
    except Exception as e:
        logger.error(f"❌ Erreur de connexion DB: {e}")
        return False

def test_scraper_connectivity():
    """Tester la connectivité vers GlobalRoamer"""
    logger.info("\n🌐 TEST DE CONNECTIVITÉ GLOBALROAMER")
    logger.info("=" * 50)
    
    try:
        import requests
        import urllib3
        
        # Désactiver les avertissements SSL
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # Tester la connectivité de base
        urls = [
            "http://globalroamer.com",
            "https://globalroamer.com",
            "http://globalroamer.com/webui/faces/login.xhtml"
        ]
        
        for url in urls:
            try:
                response = requests.get(url, timeout=10, verify=False)
                logger.info(f"✅ {url} - Status: {response.status_code}")
                return True
            except Exception as e:
                logger.warning(f"⚠️ {url} - Erreur: {e}")
        
        logger.error("❌ Aucune URL GlobalRoamer accessible")
        return False
        
    except Exception as e:
        logger.error(f"❌ Erreur de test de connectivité: {e}")
        return False

def test_csv_processing():
    """Tester le traitement des fichiers CSV"""
    logger.info("\n📁 TEST DE TRAITEMENT CSV")
    logger.info("=" * 50)
    
    try:
        # Vérifier si des fichiers CSV de test existent
        test_data_dir = Path(__file__).parent / "test_data"
        csv_files = list(test_data_dir.glob("*.csv"))
        
        if csv_files:
            logger.info(f"✅ {len(csv_files)} fichier(s) CSV de test trouvé(s)")
            
            # Tester le traitement d'un fichier
            test_file = csv_files[0]
            logger.info(f"📄 Test avec: {test_file.name}")
            
            import pandas as pd
            df = pd.read_csv(test_file, encoding='utf-8', low_memory=False)
            logger.info(f"📊 {len(df)} lignes lues")
            logger.info(f"📋 {len(df.columns)} colonnes: {list(df.columns[:5])}...")
            
            return True
        else:
            logger.warning("⚠️ Aucun fichier CSV de test trouvé")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erreur de traitement CSV: {e}")
        return False

def test_automation_service():
    """Tester le service d'automatisation"""
    logger.info("\n🤖 TEST DU SERVICE D'AUTOMATISATION")
    logger.info("=" * 50)
    
    try:
        from automation_service import AutomationService
        
        # Créer le service
        service = AutomationService()
        logger.info("✅ Service d'automatisation créé")
        
        # Vérifier les statistiques
        stats = service.get_stats()
        logger.info(f"📊 Statistiques:")
        logger.info(f"   - Total runs: {stats.get('total_runs', 0)}")
        logger.info(f"   - Successful runs: {stats.get('successful_runs', 0)}")
        logger.info(f"   - Failed runs: {stats.get('failed_runs', 0)}")
        logger.info(f"   - Last success: {stats.get('last_success', 'Jamais')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur du service d'automatisation: {e}")
        return False

def test_schedule_simulation():
    """Simuler l'exécution programmée"""
    logger.info("\n⏰ TEST DE SIMULATION DE PLANIFICATION")
    logger.info("=" * 50)
    
    try:
        import schedule
        
        # Créer une tâche de test
        def test_job():
            logger.info("✅ Tâche de test exécutée!")
            return True
        
        # Programmer la tâche (pour test immédiat)
        schedule.every(1).seconds.do(test_job)
        
        # Exécuter une fois
        schedule.run_pending()
        
        # Nettoyer
        schedule.clear()
        
        logger.info("✅ Système de planification fonctionne")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur de planification: {e}")
        return False

def generate_test_report():
    """Générer un rapport de test complet"""
    logger.info("\n📋 RAPPORT DE TEST COMPLET")
    logger.info("=" * 50)
    
    tests = [
        ("Environnement", test_environment),
        ("Base de données", test_database_connection),
        ("Connectivité GlobalRoamer", test_scraper_connectivity),
        ("Traitement CSV", test_csv_processing),
        ("Service d'automatisation", test_automation_service),
        ("Planification", test_schedule_simulation)
    ]
    
    results = {}
    total_tests = len(tests)
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            logger.error(f"❌ Erreur dans {test_name}: {e}")
            results[test_name] = False
    
    # Résumé final
    logger.info("\n🎯 RÉSUMÉ FINAL")
    logger.info("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name}: {status}")
    
    success_rate = (passed_tests / total_tests) * 100
    logger.info(f"\n📊 Taux de réussite: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        logger.info("🎉 Système globalement fonctionnel!")
    elif success_rate >= 60:
        logger.warning("⚠️ Système partiellement fonctionnel - Corrections nécessaires")
    else:
        logger.error("❌ Système défaillant - Corrections urgentes requises")
    
    return results

def main():
    """Fonction principale"""
    logger.info("🚀 DÉMARRAGE DES TESTS DU SYSTÈME DE SCRAPING")
    logger.info("=" * 60)
    logger.info(f"Date/Heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        results = generate_test_report()
        
        # Sauvegarder le rapport
        report_file = Path(__file__).parent / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale lors des tests: {e}")
        return False

if __name__ == "__main__":
    main()
