#!/usr/bin/env python3
"""
Module d'import des données CSV dans la base de données MySQL
"""

import pandas as pd
import pymysql
import os
import logging
from datetime import datetime
from typing import List, Dict, Optional
import numpy as np
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CSVImporter:
    """Classe pour importer les données CSV dans la base de données"""
    
    def __init__(self):
        """Initialiser l'importeur avec la configuration de la base de données"""
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'user': os.getenv('DB_USER', 'root'),
            'password': os.getenv('DB_PASSWORD', ''),
            'database': os.getenv('DB_NAME', 'kpi'),
            'port': int(os.getenv('DB_PORT', '3306')),
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            logger.error(f"❌ Erreur de connexion à la base de données: {e}")
            raise
    
    def clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Nettoyer et préparer le DataFrame pour l'import"""
        try:
            logger.info("🧹 Nettoyage des données CSV...")
            logger.info(f"📊 Colonnes dans le CSV: {list(df.columns)}")

            # Copier le DataFrame pour éviter les modifications sur l'original
            cleaned_df = df.copy()

            # Remplacer les valeurs NaN par None (NULL en SQL)
            cleaned_df = cleaned_df.where(pd.notnull(cleaned_df), None)

            # Identifier automatiquement les colonnes de texte (type object ou string)
            text_columns = []
            for col in cleaned_df.columns:
                if cleaned_df[col].dtype == 'object' or str(cleaned_df[col].dtype).startswith('string'):
                    text_columns.append(col)
            
            logger.info(f"📝 Colonnes de texte détectées: {text_columns}")

            for col in text_columns:
                if col in cleaned_df.columns and cleaned_df[col].notna().any():
                    # Convertir en string et limiter la longueur selon le type de colonne
                    cleaned_df[col] = cleaned_df[col].astype(str)

                    # Limiter la longueur selon les contraintes de la base de données
                    if 'Text' in col or 'Path' in col or 'Description' in col:
                        cleaned_df[col] = cleaned_df[col].str[:1000]  # Colonnes de texte long
                    elif 'Name' in col and 'Short' not in col:
                        cleaned_df[col] = cleaned_df[col].str[:255]   # Noms d'opérateurs
                    elif 'country' in col.lower() or 'location' in col.lower():
                        cleaned_df[col] = cleaned_df[col].str[:100]   # Pays et locations
                    else:
                        cleaned_df[col] = cleaned_df[col].str[:50]    # Autres textes courts

            # Identifier automatiquement les colonnes numériques
            numeric_columns = []
            for col in cleaned_df.columns:
                if col not in text_columns and col != 'Timestamp':
                    # Essayer de convertir en numérique pour voir si c'est possible
                    try:
                        pd.to_numeric(cleaned_df[col], errors='raise')
                        numeric_columns.append(col)
                    except (ValueError, TypeError):
                        pass

            logger.info(f"🔢 Colonnes numériques détectées: {numeric_columns}")

            for col in numeric_columns:
                if col in cleaned_df.columns:
                    cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce')
            
            # Nettoyer les colonnes booléennes
            boolean_columns = ['Completed', 'Failure', 'Incomplete', 'L3_Flag', 'Success']
            for col in boolean_columns:
                if col in cleaned_df.columns:
                    cleaned_df[col] = cleaned_df[col].astype(bool).astype(int)
            
            # Nettoyer les colonnes d'attachement
            attachment_columns = ['attache0', 'attache1', 'attache2', 'attache3', 
                                'attache4', 'attache5', 'attache6']
            for col in attachment_columns:
                if col in cleaned_df.columns:
                    cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce')
                    cleaned_df[col] = cleaned_df[col].fillna(0.0)
            
            # Traiter la colonne Timestamp
            if 'Timestamp' in cleaned_df.columns:
                cleaned_df['Timestamp'] = pd.to_datetime(cleaned_df['Timestamp'], errors='coerce')
            
            # Ajouter les colonnes temporelles si elles n'existent pas
            if 'Timestamp' in cleaned_df.columns and not cleaned_df['Timestamp'].isna().all():
                if 'DayOfMonth' not in cleaned_df.columns:
                    cleaned_df['DayOfMonth'] = cleaned_df['Timestamp'].dt.day
                if 'DayOfWeek' not in cleaned_df.columns:
                    cleaned_df['DayOfWeek'] = cleaned_df['Timestamp'].dt.day_name()
                if 'HourOfDay' not in cleaned_df.columns:
                    cleaned_df['HourOfDay'] = cleaned_df['Timestamp'].dt.hour
                if 'MonthOfYear' not in cleaned_df.columns:
                    cleaned_df['MonthOfYear'] = cleaned_df['Timestamp'].dt.month
                if 'WeekOfYear' not in cleaned_df.columns:
                    cleaned_df['WeekOfYear'] = cleaned_df['Timestamp'].dt.isocalendar().week
            
            logger.info(f"✅ Données nettoyées - {len(cleaned_df)} lignes")
            return cleaned_df
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du nettoyage des données: {e}")
            raise
    
    def import_csv_file(self, csv_file_path: str, batch_size: int = 1000) -> bool:
        """
        Importer un fichier CSV dans la base de données
        
        Args:
            csv_file_path: Chemin vers le fichier CSV
            batch_size: Taille des lots pour l'insertion
            
        Returns:
            True si l'import a réussi, False sinon
        """
        try:
            logger.info(f"📥 Import du fichier: {csv_file_path}")
            
            # Lire le fichier CSV
            df = pd.read_csv(csv_file_path, encoding='utf-8', low_memory=False)
            logger.info(f"📊 {len(df)} lignes lues depuis le CSV")
            
            # Nettoyer les données
            cleaned_df = self.clean_dataframe(df)
            
            # Obtenir la connexion à la base de données
            connection = self.get_connection()
            
            try:
                with connection.cursor() as cursor:
                    # Préparer la requête d'insertion
                    columns = list(cleaned_df.columns)
                    placeholders = ', '.join(['%s'] * len(columns))
                    columns_str = ', '.join([f"`{col}`" for col in columns])
                    
                    insert_query = f"""
                    INSERT INTO steeringofroaming ({columns_str})
                    VALUES ({placeholders})
                    ON DUPLICATE KEY UPDATE
                    TestcaseId = VALUES(TestcaseId)
                    """
                    
                    # Insérer par lots
                    total_inserted = 0
                    for i in range(0, len(cleaned_df), batch_size):
                        batch = cleaned_df.iloc[i:i+batch_size]
                        
                        # Convertir le batch en liste de tuples
                        batch_data = []
                        for _, row in batch.iterrows():
                            row_data = []
                            for col in columns:
                                value = row[col]
                                if pd.isna(value):
                                    row_data.append(None)
                                elif isinstance(value, (np.integer, np.floating)):
                                    if np.isnan(value):
                                        row_data.append(None)
                                    else:
                                        row_data.append(float(value) if isinstance(value, np.floating) else int(value))
                                else:
                                    row_data.append(value)
                            batch_data.append(tuple(row_data))
                        
                        # Exécuter l'insertion
                        cursor.executemany(insert_query, batch_data)
                        total_inserted += len(batch_data)
                        
                        logger.info(f"📝 {total_inserted}/{len(cleaned_df)} lignes insérées...")
                    
                    # Valider les changements
                    connection.commit()
                    logger.info(f"✅ Import terminé - {total_inserted} lignes insérées")
                    
                    return True
                    
            except Exception as e:
                connection.rollback()
                logger.error(f"❌ Erreur lors de l'insertion: {e}")
                return False
                
            finally:
                connection.close()
                
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'import du fichier {csv_file_path}: {e}")
            return False
    
    def import_multiple_files(self, csv_files: List[str]) -> Dict[str, bool]:
        """
        Importer plusieurs fichiers CSV
        
        Args:
            csv_files: Liste des chemins vers les fichiers CSV
            
        Returns:
            Dictionnaire avec le statut d'import pour chaque fichier
        """
        results = {}
        
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                results[csv_file] = self.import_csv_file(csv_file)
            else:
                logger.warning(f"⚠️ Fichier non trouvé: {csv_file}")
                results[csv_file] = False
        
        # Résumé
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"📊 Résumé de l'import: {successful}/{total} fichiers importés avec succès")
        
        return results
    
    def get_import_stats(self) -> Dict:
        """Obtenir les statistiques d'import"""
        try:
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                # Compter le total d'enregistrements
                cursor.execute("SELECT COUNT(*) as total FROM steeringofroaming")
                total_records = cursor.fetchone()['total']
                
                # Compter par pays
                cursor.execute("""
                    SELECT a_location_country, COUNT(*) as count 
                    FROM steeringofroaming 
                    WHERE a_location_country IS NOT NULL 
                    GROUP BY a_location_country 
                    ORDER BY count DESC 
                    LIMIT 10
                """)
                top_countries = cursor.fetchall()
                
                # Compter par verdict
                cursor.execute("""
                    SELECT Verdict, COUNT(*) as count 
                    FROM steeringofroaming 
                    GROUP BY Verdict
                """)
                verdict_stats = cursor.fetchall()
                
                # Date du dernier enregistrement
                cursor.execute("""
                    SELECT MAX(Timestamp) as last_record, MIN(Timestamp) as first_record
                    FROM steeringofroaming 
                    WHERE Timestamp IS NOT NULL
                """)
                date_range = cursor.fetchone()
                
                return {
                    'total_records': total_records,
                    'top_countries': top_countries,
                    'verdict_stats': verdict_stats,
                    'date_range': date_range
                }
                
        except Exception as e:
            logger.error(f"❌ Erreur lors de la récupération des statistiques: {e}")
            return {}
        finally:
            connection.close()


# Fonction utilitaire pour tester l'importeur
def test_importer():
    """Fonction de test de l'importeur"""
    importer = CSVImporter()
    
    # Afficher les statistiques actuelles
    stats = importer.get_import_stats()
    print("📊 Statistiques actuelles:")
    print(f"   Total d'enregistrements: {stats.get('total_records', 0)}")
    print(f"   Top pays: {stats.get('top_countries', [])[:3]}")
    
    return importer

if __name__ == "__main__":
    test_importer()
