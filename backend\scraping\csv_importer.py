#!/usr/bin/env python3
"""
Module d'import des données CSV dans la base de données MySQL
Garantit que toutes les colonnes du CSV sont correctement importées
"""

import pandas as pd
import pymysql
import os
import logging
from datetime import datetime
from typing import List, Dict, Optional
import numpy as np
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CSVImporter:
    """Classe pour importer les données CSV dans la base de données"""
    
    def __init__(self):
        """Initialiser l'importeur avec la configuration de la base de données"""
        self.db_config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'user': os.getenv('DB_USER', 'root'),
            'password': os.getenv('DB_PASSWORD', ''),
            'database': os.getenv('DB_NAME', 'kpi'),
            'port': int(os.getenv('DB_PORT', '3306')),
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
    
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        try:
            connection = pymysql.connect(**self.db_config)
            return connection
        except Exception as e:
            logger.error(f"❌ Erreur de connexion à la base de données: {e}")
            raise

    def ensure_table_columns(self, connection, table_name: str, df: pd.DataFrame) -> bool:
        """
        S'assure que toutes les colonnes du DataFrame existent dans la table
        Args:
            connection: Connexion à la base de données
            table_name: Nom de la table
            df: DataFrame contenant les données
        Returns:
            True si la structure est correcte ou a été mise à jour
        """
        try:
            with connection.cursor() as cursor:
                # Obtenir les colonnes existantes
                cursor.execute(f"SHOW COLUMNS FROM {table_name}")
                existing_columns = {row['Field'] for row in cursor.fetchall()}
                
                # Vérifier les colonnes manquantes
                df_columns = set(df.columns)
                missing_columns = df_columns - existing_columns
                
                if missing_columns:
                    logger.warning(f"⚠️ Colonnes manquantes dans la table: {missing_columns}")
                    
                    # Ajouter les colonnes manquantes
                    for col in missing_columns:
                        # Déterminer le type SQL approprié
                        if col in df and df[col].dtype == 'object':
                            sql_type = 'TEXT'
                        elif col in df and np.issubdtype(df[col].dtype, np.number):
                            if df[col].dtype == 'int64':
                                sql_type = 'BIGINT'
                            else:
                                sql_type = 'DOUBLE'
                        else:
                            sql_type = 'TEXT'  # Par défaut
                            
                        alter_query = f"ALTER TABLE {table_name} ADD COLUMN `{col}` {sql_type}"
                        logger.info(f"➕ Ajout de la colonne {col} ({sql_type})")
                        cursor.execute(alter_query)
                
                connection.commit()
                return True
                
        except Exception as e:
            logger.error(f"❌ Erreur lors de la mise à jour de la structure: {e}")
            connection.rollback()
            return False

    def clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Nettoyer et préparer le DataFrame pour l'import"""
        try:
            logger.info("🧹 Nettoyage des données CSV...")
            logger.info(f"📊 Colonnes dans le CSV: {list(df.columns)}")
            
            # Copier le DataFrame pour éviter les modifications sur l'original
            cleaned_df = df.copy()
            
            # Convertir les colonnes en types appropriés
            for col in cleaned_df.columns:
                # Si la colonne contient des données numériques
                if cleaned_df[col].dtype in ['int64', 'float64']:
                    continue  # Garder le type numérique
                
                # Pour les autres colonnes, convertir en string et nettoyer
                if cleaned_df[col].dtype == 'object':
                    cleaned_df[col] = cleaned_df[col].astype(str)
                    # Remplacer 'nan' et 'None' par None
                    cleaned_df[col] = cleaned_df[col].replace({'nan': None, 'None': None, '': None})
            
            logger.info(f"✅ Données nettoyées - {len(cleaned_df)} lignes")
            return cleaned_df
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du nettoyage des données: {e}")
            raise

    def import_csv_file(self, csv_file_path: str, batch_size: int = 1000) -> bool:
        """
        Importer un fichier CSV dans la base de données
        Args:
            csv_file_path: Chemin vers le fichier CSV
            batch_size: Taille des lots pour l'insertion
        Returns:
            True si l'import a réussi, False sinon
        """
        try:
            logger.info(f"📥 Import du fichier: {csv_file_path}")
            
            # Lire le fichier CSV avec tous les types en string
            df = pd.read_csv(csv_file_path, encoding='utf-8', dtype=str, na_filter=False)
            logger.info(f"📊 {len(df)} lignes lues depuis le CSV")
            
            # Nettoyer les données
            cleaned_df = self.clean_dataframe(df)
            
            # Obtenir la connexion à la base de données
            connection = self.get_connection()
            
            try:
                # Vérifier et mettre à jour la structure de la table si nécessaire
                if not self.ensure_table_columns(connection, 'steeringofroaming', cleaned_df):
                    raise Exception("Impossible de mettre à jour la structure de la table")
                
                with connection.cursor() as cursor:
                    # Préparer la requête d'insertion
                    columns = list(cleaned_df.columns)
                    placeholders = ', '.join(['%s'] * len(columns))
                    columns_str = ', '.join([f"`{col}`" for col in columns])
                    
                    insert_query = f"""
                    INSERT INTO steeringofroaming ({columns_str})
                    VALUES ({placeholders})
                    ON DUPLICATE KEY UPDATE
                    {', '.join([f"`{col}` = VALUES(`{col}`)" for col in columns])}
                    """
                    
                    # Insérer par lots
                    total_inserted = 0
                    for i in range(0, len(cleaned_df), batch_size):
                        batch = cleaned_df.iloc[i:i+batch_size]
                        
                        # Convertir le batch en liste de tuples
                        batch_data = [tuple(row) for _, row in batch.iterrows()]
                        
                        # Exécuter l'insertion
                        cursor.executemany(insert_query, batch_data)
                        total_inserted += len(batch_data)
                        
                        logger.info(f"📝 {total_inserted}/{len(cleaned_df)} lignes insérées...")
                    
                    # Valider les changements
                    connection.commit()
                    logger.info(f"✅ Import terminé - {total_inserted} lignes insérées")
                    
                    return True
                    
            except Exception as e:
                connection.rollback()
                logger.error(f"❌ Erreur lors de l'insertion: {e}")
                return False
                
            finally:
                connection.close()
                
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'import du fichier {csv_file_path}: {e}")
            return False

    def import_multiple_files(self, csv_files: List[str]) -> Dict[str, bool]:
        """
        Importer plusieurs fichiers CSV
        Args:
            csv_files: Liste des chemins vers les fichiers CSV
        Returns:
            Dictionnaire avec le statut d'import pour chaque fichier
        """
        results = {}
        
        for csv_file in csv_files:
            if os.path.exists(csv_file):
                results[csv_file] = self.import_csv_file(csv_file)
            else:
                logger.warning(f"⚠️ Fichier non trouvé: {csv_file}")
                results[csv_file] = False
        
        # Résumé
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        logger.info(f"📊 Résumé de l'import: {successful}/{total} fichiers importés avec succès")
        
        return results

    def get_import_stats(self) -> Dict:
        """Obtenir les statistiques d'import"""
        try:
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                # Compter le total d'enregistrements
                cursor.execute("SELECT COUNT(*) as total FROM steeringofroaming")
                result = cursor.fetchone()
                total_records = result['total'] if result else 0
                
                return {
                    'total_records': total_records
                }
                
        except Exception as e:
            logger.error(f"❌ Erreur lors de la récupération des statistiques: {e}")
            return {'total_records': 0}
        finally:
            if connection:
                connection.close()

if __name__ == "__main__":
    importer = CSVImporter()
    # Test avec un fichier CSV
    test_file = "test_data/SteeringOfRoaming_test.csv"
    if os.path.exists(test_file):
        importer.import_csv_file(test_file)
