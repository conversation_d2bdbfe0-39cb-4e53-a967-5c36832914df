#!/usr/bin/env python3
"""
Script de test de connectivité pour GlobalRoamer
"""

import requests
import socket
import subprocess
import sys
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_internet_connection():
    """Tester la connexion internet générale"""
    print("🌐 Test de la connexion internet...")
    
    try:
        # Test avec Google DNS
        socket.create_connection(("*******", 53), timeout=10)
        print("✅ Connexion internet OK")
        return True
    except OSError:
        print("❌ Pas de connexion internet")
        return False

def test_dns_resolution():
    """Tester la résolution DNS pour GlobalRoamer"""
    print("🔍 Test de résolution DNS pour globalroamer.com...")
    
    try:
        ip = socket.gethostbyname("globalroamer.com")
        print(f"✅ DNS résolu: globalroamer.com -> {ip}")
        return True
    except socket.gaierror as e:
        print(f"❌ Erreur DNS: {e}")
        return False

def test_http_connection():
    """Tester la connexion HTTP vers GlobalRoamer"""
    print("🌐 Test de connexion HTTP vers GlobalRoamer...")
    
    urls_to_test = [
        "https://globalroamer.com",
        "https://globalroamer.com",
        "https://globalroamer.com/webui/faces/login.xhtml"
    ]
    
    for url in urls_to_test:
        try:
            print(f"Test de {url}...")
            response = requests.get(url, timeout=30, verify=False, allow_redirects=True)
            print(f"✅ {url} - Status: {response.status_code}")
            if response.status_code == 200:
                return True
        except requests.exceptions.RequestException as e:
            print(f"❌ {url} - Erreur: {e}")
    
    return False

def test_chrome_driver():
    """Tester l'installation et le fonctionnement de ChromeDriver"""
    print("🚗 Test de ChromeDriver...")
    
    try:
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Test simple
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ ChromeDriver fonctionne - Page de test: {title}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur ChromeDriver: {e}")
        return False

def test_selenium_with_globalroamer():
    """Tester Selenium avec GlobalRoamer"""
    print("🔧 Test de Selenium avec GlobalRoamer...")
    
    try:
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Timeouts plus longs
        driver.set_page_load_timeout(60)
        driver.implicitly_wait(20)
        
        # Tenter d'accéder à GlobalRoamer
        print("Tentative d'accès à GlobalRoamer...")
        driver.get("https://globalroamer.com/webui/faces/login.xhtml")
        
        print(f"✅ Page chargée - Titre: {driver.title}")
        print(f"URL actuelle: {driver.current_url}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ Erreur Selenium: {e}")
        return False

def test_proxy_settings():
    """Vérifier les paramètres de proxy"""
    print("🔧 Vérification des paramètres de proxy...")
    
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    proxy_found = False
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"⚠️ Proxy détecté: {var}={value}")
            proxy_found = True
    
    if not proxy_found:
        print("✅ Aucun proxy configuré")
    
    return not proxy_found

def ping_test():
    """Test de ping vers GlobalRoamer"""
    print("🏓 Test de ping vers globalroamer.com...")
    
    try:
        if sys.platform.startswith('win'):
            result = subprocess.run(['ping', '-n', '4', 'globalroamer.com'], 
                                  capture_output=True, text=True, timeout=30)
        else:
            result = subprocess.run(['ping', '-c', '4', 'globalroamer.com'], 
                                  capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Ping réussi")
            print(result.stdout.split('\n')[-3:-1])  # Afficher les statistiques
            return True
        else:
            print("❌ Ping échoué")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Erreur ping: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🔍 DIAGNOSTIC DE CONNECTIVITÉ GLOBALROAMER")
    print("=" * 50)
    
    tests = [
        ("Connexion Internet", test_internet_connection),
        ("Résolution DNS", test_dns_resolution),
        ("Test Ping", ping_test),
        ("Connexion HTTP", test_http_connection),
        ("Paramètres Proxy", test_proxy_settings),
        ("ChromeDriver", test_chrome_driver),
        ("Selenium + GlobalRoamer", test_selenium_with_globalroamer)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        print("-" * 30)
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Erreur inattendue: {e}")
            results[test_name] = False
    
    # Résumé
    print("\n📊 RÉSUMÉ DES TESTS:")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
    
    # Recommandations
    print("\n💡 RECOMMANDATIONS:")
    print("=" * 50)
    
    if not results.get("Connexion Internet", False):
        print("- Vérifiez votre connexion internet")
    
    if not results.get("Résolution DNS", False):
        print("- Problème DNS - Essayez de changer vos serveurs DNS (*******, 1.1.1.1)")
    
    if not results.get("Connexion HTTP", False):
        print("- Le site GlobalRoamer pourrait être inaccessible ou bloqué")
        print("- Vérifiez si vous êtes derrière un firewall d'entreprise")
    
    if not results.get("Paramètres Proxy", True):
        print("- Configurez Selenium pour utiliser votre proxy")
    
    if not results.get("ChromeDriver", False):
        print("- Réinstallez ChromeDriver ou mettez à jour Chrome")
    
    print("\n🏁 Diagnostic terminé!")

if __name__ == "__main__":
    main()
