#!/usr/bin/env python3
"""
Script de configuration pour le système de web scraping GlobalRoamer
"""

import os
import subprocess
import sys
from pathlib import Path

def install_requirements():
    """Installer les dépendances nécessaires"""
    print("📦 Installation des dépendances pour le web scraping...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements_scraping.txt"
        ])
        print("✅ Dépendances installées avec succès")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'installation des dépendances: {e}")
        return False

def setup_env_file():
    """Configurer le fichier .env avec les variables de scraping"""
    env_path = Path("backend/.env")
    
    print("🔧 Configuration du fichier .env...")
    
    # Lire le fichier .env existant
    env_content = ""
    if env_path.exists():
        with open(env_path, 'r') as f:
            env_content = f.read()
    
    # Ajouter les variables de scraping si elles n'existent pas
    scraping_vars = [
        "# Configuration GlobalRoamer pour le web scraping",
        "GLOBALROAMER_USERNAME=meditel_ma_read",
        "GLOBALROAMER_PASSWORD=j(O1*54F"
    ]
    
    needs_update = False
    for var in scraping_vars:
        if "GLOBALROAMER_USERNAME" in var and "GLOBALROAMER_USERNAME" not in env_content:
            needs_update = True
            break
    
    if needs_update:
        with open(env_path, 'a') as f:
            f.write("\n\n")
            for var in scraping_vars:
                f.write(var + "\n")
        
        print("✅ Variables de scraping ajoutées au fichier .env")
        print("⚠️  IMPORTANT: Modifiez le fichier backend/.env avec vos vrais identifiants GlobalRoamer")
    else:
        print("✅ Variables de scraping déjà présentes dans .env")

def create_directories():
    """Créer les répertoires nécessaires"""
    print("📁 Création des répertoires...")
    
    directories = [
        "backend/scraping",
        "downloads/globalroamer",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ {directory}")

def test_selenium_setup():
    """Tester la configuration de Selenium"""
    print("🧪 Test de la configuration Selenium...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        # Configuration de test
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        # Installer ChromeDriver automatiquement
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Test simple
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ Selenium configuré correctement - Test réussi (titre: {title[:30]}...)")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de configuration Selenium: {e}")
        print("💡 Assurez-vous que Chrome est installé sur votre système")
        return False

def show_usage_instructions():
    """Afficher les instructions d'utilisation"""
    print("\n" + "="*60)
    print("🎉 CONFIGURATION TERMINÉE!")
    print("="*60)
    
    print("\n📋 PROCHAINES ÉTAPES:")
    print("1. Modifiez backend/.env avec vos identifiants GlobalRoamer:")
    print("   GLOBALROAMER_USERNAME=votre_vrai_username")
    print("   GLOBALROAMER_PASSWORD=votre_vrai_password")
    
    print("\n2. Démarrez votre backend:")
    print("   cd backend")
    print("   python -m uvicorn main:app --host 0.0.0.0 --reload")
    
    print("\n3. Testez le scraping via l'API:")
    print("   GET  http://localhost:8000/api/scraping/config")
    print("   POST http://localhost:8000/api/scraping/manual?days_back=1")
    print("   GET  http://localhost:8000/api/scraping/status")
    
    print("\n4. Ou utilisez le service d'automatisation:")
    print("   cd backend/scraping")
    print("   python automation_service.py")
    
    print("\n🔧 ENDPOINTS DISPONIBLES:")
    print("   /api/scraping/config  - Configuration du scraping")
    print("   /api/scraping/status  - Statut du service")
    print("   /api/scraping/run     - Lancer en arrière-plan")
    print("   /api/scraping/manual  - Exécution manuelle")
    
    print("\n⚠️  IMPORTANT:")
    print("   - Assurez-vous que XAMPP/MySQL est démarré")
    print("   - Vérifiez que la base 'kpi' existe")
    print("   - Testez d'abord avec days_back=1")

def main():
    """Fonction principale de configuration"""
    print("🚀 Configuration du système de web scraping GlobalRoamer")
    print("="*60)
    
    # Étape 1: Créer les répertoires
    create_directories()
    
    # Étape 2: Installer les dépendances
    if not install_requirements():
        print("❌ Échec de l'installation des dépendances")
        return False
    
    # Étape 3: Configurer .env
    setup_env_file()
    
    # Étape 4: Tester Selenium
    selenium_ok = test_selenium_setup()
    
    # Étape 5: Afficher les instructions
    show_usage_instructions()
    
    if selenium_ok:
        print("\n✅ Configuration complète et fonctionnelle!")
    else:
        print("\n⚠️  Configuration partiellement réussie - Vérifiez Selenium")
    
    return True

if __name__ == "__main__":
    main()
