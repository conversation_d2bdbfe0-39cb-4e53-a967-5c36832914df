#!/usr/bin/env python3
"""
Vérification complète que TOUTES les données GlobalRoamer 
se stockent parfaitement dans la base de données kpi
"""

import os
import sys
import pandas as pd
import pymysql
import logging
from pathlib import Path
from datetime import datetime

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataVerificationTool:
    """Outil de vérification complète des données"""
    
    def __init__(self):
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'database': 'kpi',
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
    
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        return pymysql.connect(**self.db_config)
    
    def compare_csv_vs_database(self, csv_file_path: str):
        """Comparer un fichier CSV avec les données en base"""
        logger.info("🔍 COMPARAISON CSV vs BASE DE DONNÉES")
        logger.info("=" * 60)
        
        try:
            # 1. Lire le fichier CSV original
            logger.info(f"📄 Lecture du fichier CSV: {os.path.basename(csv_file_path)}")
            df_csv = pd.read_csv(csv_file_path, encoding='utf-8', low_memory=False)
            
            logger.info(f"📊 CSV - {len(df_csv)} lignes, {len(df_csv.columns)} colonnes")
            logger.info(f"📋 Colonnes CSV: {list(df_csv.columns)[:10]}...")
            
            # 2. Lire les données de la base
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                # Compter les enregistrements totaux
                cursor.execute("SELECT COUNT(*) as total FROM steeringofroaming")
                db_total = cursor.fetchone()['total']
                
                # Obtenir la structure de la table
                cursor.execute("SHOW COLUMNS FROM steeringofroaming")
                db_columns = [col['Field'] for col in cursor.fetchall()]
                
                logger.info(f"🗄️ BASE - {db_total} lignes, {len(db_columns)} colonnes")
                logger.info(f"📋 Colonnes DB: {db_columns[:10]}...")
                
                # 3. Vérifier le mapping des colonnes
                logger.info(f"\n🔗 VÉRIFICATION DU MAPPING DES COLONNES")
                logger.info("-" * 50)
                
                csv_columns = set(df_csv.columns)
                db_columns_set = set(db_columns) - {'id', 'created_at'}  # Exclure les colonnes auto-générées
                
                # Colonnes présentes dans les deux
                common_columns = csv_columns.intersection(db_columns_set)
                # Colonnes uniquement dans le CSV
                csv_only = csv_columns - db_columns_set
                # Colonnes uniquement dans la DB
                db_only = db_columns_set - csv_columns
                
                logger.info(f"✅ Colonnes communes: {len(common_columns)}")
                logger.info(f"⚠️ Colonnes CSV uniquement: {len(csv_only)}")
                logger.info(f"📋 Colonnes DB uniquement: {len(db_only)}")
                
                if csv_only:
                    logger.warning(f"⚠️ Colonnes manquantes en DB:")
                    for col in sorted(list(csv_only)[:10]):
                        logger.warning(f"   • {col}")
                
                # 4. Vérifier la qualité des données
                logger.info(f"\n📊 VÉRIFICATION DE LA QUALITÉ DES DONNÉES")
                logger.info("-" * 50)
                
                # Échantillonner quelques colonnes importantes
                key_columns = ['TestcaseId', 'TCName', 'Verdict', 'Timestamp', 'a_location_country']
                
                for col in key_columns:
                    if col in common_columns:
                        # Données CSV
                        csv_non_null = df_csv[col].notna().sum()
                        csv_unique = df_csv[col].nunique()
                        
                        # Données DB
                        cursor.execute(f"""
                            SELECT 
                                COUNT(*) as total,
                                COUNT({col}) as non_null,
                                COUNT(DISTINCT {col}) as unique_vals
                            FROM steeringofroaming
                        """)
                        db_stats = cursor.fetchone()
                        
                        logger.info(f"📋 {col}:")
                        logger.info(f"   CSV: {csv_non_null:4d} non-null, {csv_unique:4d} unique")
                        logger.info(f"   DB:  {db_stats['non_null']:4d} non-null, {db_stats['unique_vals']:4d} unique")
                        
                        # Vérifier la cohérence
                        if csv_non_null == db_stats['non_null'] and csv_unique == db_stats['unique_vals']:
                            logger.info(f"   ✅ PARFAITEMENT IDENTIQUE")
                        else:
                            logger.warning(f"   ⚠️ DIFFÉRENCES DÉTECTÉES")
                
                # 5. Vérifier des échantillons de données
                logger.info(f"\n🔬 VÉRIFICATION D'ÉCHANTILLONS DE DONNÉES")
                logger.info("-" * 50)
                
                # Prendre les 3 premiers enregistrements du CSV
                sample_csv = df_csv.head(3)
                
                for idx, row in sample_csv.iterrows():
                    testcase_id = row.get('TestcaseId')
                    if testcase_id:
                        cursor.execute(f"""
                            SELECT * FROM steeringofroaming 
                            WHERE TestcaseId = %s 
                            LIMIT 1
                        """, (testcase_id,))
                        
                        db_row = cursor.fetchone()
                        
                        if db_row:
                            logger.info(f"✅ TestcaseId {testcase_id} trouvé en DB")
                            
                            # Comparer quelques champs clés
                            matches = 0
                            total_compared = 0
                            
                            for col in ['TCName', 'Verdict', 'a_location_country']:
                                if col in row.index and col in db_row:
                                    csv_val = str(row[col]) if pd.notna(row[col]) else None
                                    db_val = db_row[col]
                                    
                                    total_compared += 1
                                    if csv_val == db_val:
                                        matches += 1
                                    else:
                                        logger.warning(f"   ⚠️ {col}: CSV='{csv_val}' vs DB='{db_val}'")
                            
                            match_rate = (matches / total_compared * 100) if total_compared > 0 else 0
                            logger.info(f"   📊 Correspondance: {matches}/{total_compared} ({match_rate:.1f}%)")
                        else:
                            logger.warning(f"⚠️ TestcaseId {testcase_id} NON TROUVÉ en DB")
            
            connection.close()
            
            # 6. Résumé final
            logger.info(f"\n🎯 RÉSUMÉ DE LA VÉRIFICATION")
            logger.info("=" * 60)
            
            column_coverage = (len(common_columns) / len(csv_columns) * 100) if len(csv_columns) > 0 else 0
            
            logger.info(f"📊 Couverture des colonnes: {len(common_columns)}/{len(csv_columns)} ({column_coverage:.1f}%)")
            
            if column_coverage >= 95:
                logger.info("🎉 EXCELLENT! Presque toutes les colonnes sont mappées")
            elif column_coverage >= 80:
                logger.info("✅ BIEN! La plupart des colonnes sont mappées")
            else:
                logger.warning("⚠️ ATTENTION! Beaucoup de colonnes manquantes")
            
            return column_coverage >= 80
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la vérification: {e}")
            return False
    
    def verify_all_tables(self):
        """Vérifier toutes les tables de données"""
        logger.info("🔍 VÉRIFICATION DE TOUTES LES TABLES")
        logger.info("=" * 60)
        
        try:
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                # Vérifier les tables existantes
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                
                logger.info(f"📋 Tables trouvées:")
                for table in tables:
                    table_name = list(table.values())[0]
                    
                    # Compter les enregistrements
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    count = cursor.fetchone()['count']
                    
                    # Compter les colonnes
                    cursor.execute(f"SHOW COLUMNS FROM {table_name}")
                    columns = cursor.fetchall()
                    
                    logger.info(f"   📊 {table_name}: {count:,} records, {len(columns)} colonnes")
                    
                    # Vérifier les données récentes
                    if 'created_at' in [col['Field'] for col in columns]:
                        cursor.execute(f"""
                            SELECT COUNT(*) as recent 
                            FROM {table_name} 
                            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                        """)
                        recent = cursor.fetchone()['recent']
                        logger.info(f"      🕐 Dernière heure: {recent:,} nouveaux records")
            
            connection.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la vérification des tables: {e}")
            return False
    
    def check_data_integrity(self):
        """Vérifier l'intégrité des données"""
        logger.info("🔒 VÉRIFICATION DE L'INTÉGRITÉ DES DONNÉES")
        logger.info("=" * 60)
        
        try:
            connection = self.get_connection()
            
            with connection.cursor() as cursor:
                # 1. Vérifier les doublons
                cursor.execute("""
                    SELECT TestcaseId, COUNT(*) as count 
                    FROM steeringofroaming 
                    GROUP BY TestcaseId 
                    HAVING count > 1 
                    LIMIT 5
                """)
                duplicates = cursor.fetchall()
                
                if duplicates:
                    logger.warning(f"⚠️ {len(duplicates)} TestcaseId en doublon détectés")
                    for dup in duplicates:
                        logger.warning(f"   • TestcaseId {dup['TestcaseId']}: {dup['count']} occurrences")
                else:
                    logger.info("✅ Aucun doublon détecté")
                
                # 2. Vérifier les données NULL critiques
                critical_fields = ['TestcaseId', 'TCName', 'Verdict', 'Timestamp']
                
                for field in critical_fields:
                    cursor.execute(f"""
                        SELECT COUNT(*) as null_count 
                        FROM steeringofroaming 
                        WHERE {field} IS NULL OR {field} = ''
                    """)
                    null_count = cursor.fetchone()['null_count']
                    
                    if null_count > 0:
                        logger.warning(f"⚠️ {field}: {null_count} valeurs NULL/vides")
                    else:
                        logger.info(f"✅ {field}: Aucune valeur NULL")
                
                # 3. Vérifier la cohérence des timestamps
                cursor.execute("""
                    SELECT 
                        MIN(Timestamp) as oldest,
                        MAX(Timestamp) as newest,
                        COUNT(*) as total
                    FROM steeringofroaming 
                    WHERE Timestamp IS NOT NULL
                """)
                time_stats = cursor.fetchone()
                
                if time_stats['total'] > 0:
                    logger.info(f"📅 Période des données: {time_stats['oldest']} → {time_stats['newest']}")
                    logger.info(f"📊 {time_stats['total']} enregistrements avec timestamp")
            
            connection.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la vérification d'intégrité: {e}")
            return False

def main():
    """Fonction principale de vérification"""
    logger.info("🚀 VÉRIFICATION COMPLÈTE DU STOCKAGE DES DONNÉES")
    logger.info("=" * 70)
    logger.info(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    verifier = DataVerificationTool()
    
    # 1. Vérifier toutes les tables
    logger.info("\n1️⃣ VÉRIFICATION DES TABLES")
    tables_ok = verifier.verify_all_tables()
    
    # 2. Vérifier l'intégrité des données
    logger.info("\n2️⃣ VÉRIFICATION DE L'INTÉGRITÉ")
    integrity_ok = verifier.check_data_integrity()
    
    # 3. Comparer avec un fichier CSV si disponible
    logger.info("\n3️⃣ COMPARAISON CSV vs BASE DE DONNÉES")
    test_csv = Path(__file__).parent / "test_data" / "SteeringOfRoaming_20250706_test.csv"
    
    if test_csv.exists():
        csv_comparison_ok = verifier.compare_csv_vs_database(str(test_csv))
    else:
        logger.warning("⚠️ Fichier CSV de test non trouvé pour comparaison")
        csv_comparison_ok = True
    
    # Résumé final
    logger.info(f"\n🎯 RÉSUMÉ FINAL DE LA VÉRIFICATION")
    logger.info("=" * 70)
    
    all_checks = [tables_ok, integrity_ok, csv_comparison_ok]
    passed_checks = sum(all_checks)
    
    logger.info(f"📊 Tests réussis: {passed_checks}/3")
    
    if passed_checks == 3:
        logger.info("🎉 PARFAIT! TOUTES LES DONNÉES SONT STOCKÉES COMPLÈTEMENT!")
        logger.info("✅ Les données GlobalRoamer sont identiques en base de données")
        logger.info("✅ Toutes les colonnes sont correctement mappées")
        logger.info("✅ L'intégrité des données est préservée")
    elif passed_checks >= 2:
        logger.info("✅ BIEN! Le stockage fonctionne correctement")
        logger.info("🔧 Quelques améliorations mineures possibles")
    else:
        logger.warning("⚠️ ATTENTION! Problèmes de stockage détectés")
        logger.warning("🚨 Vérification et corrections nécessaires")
    
    return passed_checks >= 2

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
