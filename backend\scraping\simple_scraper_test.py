#!/usr/bin/env python3
"""
Test simple du scraper sans WebDriver pour diagnostiquer les problèmes
"""

import os
import requests
import urllib3
from dotenv import load_dotenv

# Désactiver les avertissements SSL
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Charger les variables d'environnement depuis le bon fichier
env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(env_path)

def test_basic_connectivity():
    """Test de connectivité de base"""
    print("🌐 TEST DE CONNECTIVITÉ DE BASE")
    print("=" * 40)
    
    # Test de sites de référence
    test_sites = [
        "https://www.google.com",
        "https://httpbin.org/get",
        "http://httpbin.org/get"
    ]
    
    for site in test_sites:
        try:
            print(f"Test de {site}...")
            response = requests.get(site, timeout=10, verify=False)
            print(f"✅ {site} - Status: {response.status_code}")
        except Exception as e:
            print(f"❌ {site} - Erreur: {e}")

def test_globalroamer_connectivity():
    """Test spécifique à GlobalRoamer"""
    print("\n🎯 TEST GLOBALROAMER")
    print("=" * 40)
    
    urls = [
        "http://globalroamer.com",
        "https://globalroamer.com", 
        "https://globalroamer.com/webui/faces/login.xhtml"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    for url in urls:
        try:
            print(f"Test de {url}...")
            response = requests.get(
                url, 
                timeout=30, 
                verify=False, 
                allow_redirects=True,
                headers=headers
            )
            print(f"✅ {url}")
            print(f"   Status: {response.status_code}")
            print(f"   URL finale: {response.url}")
            print(f"   Taille: {len(response.content)} bytes")
            
            # Vérifier si c'est une page de login
            if "login" in response.text.lower() or "username" in response.text.lower():
                print("   🔐 Page de login détectée")
            
            return True
            
        except requests.exceptions.SSLError as e:
            print(f"❌ {url} - Erreur SSL: {e}")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ {url} - Erreur de connexion: {e}")
        except requests.exceptions.Timeout as e:
            print(f"❌ {url} - Timeout: {e}")
        except Exception as e:
            print(f"❌ {url} - Erreur: {e}")
    
    return False

def test_credentials():
    """Test des identifiants"""
    print("\n🔑 TEST DES IDENTIFIANTS")
    print("=" * 40)

    # Debug: vérifier le chemin du fichier .env
    env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    print(f"Chemin .env: {os.path.abspath(env_path)}")
    print(f"Fichier .env existe: {os.path.exists(env_path)}")

    username = os.getenv('GLOBALROAMER_USERNAME')
    password = os.getenv('GLOBALROAMER_PASSWORD')
    
    if not username or not password:
        print("❌ Identifiants manquants dans .env")
        return False
    
    print(f"✅ Username: {username}")
    print(f"✅ Password: {'*' * len(password)} ({len(password)} caractères)")
    
    # Vérifier que ce ne sont pas les valeurs par défaut
    if "votre_" in username.lower() or "your_" in username.lower():
        print("❌ Username semble être une valeur par défaut")
        return False
    
    if "votre_" in password.lower() or "your_" in password.lower():
        print("❌ Password semble être une valeur par défaut")
        return False
    
    print("✅ Identifiants semblent valides")
    return True

def simulate_login_attempt():
    """Simuler une tentative de login avec requests"""
    print("\n🔐 SIMULATION DE LOGIN")
    print("=" * 40)
    
    username = os.getenv('GLOBALROAMER_USERNAME')
    password = os.getenv('GLOBALROAMER_PASSWORD')
    
    if not username or not password:
        print("❌ Identifiants manquants")
        return False
    
    try:
        # Créer une session
        session = requests.Session()
        session.verify = False
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        # Obtenir la page de login (utiliser HTTP pour éviter les problèmes SSL)
        login_url = "http://globalroamer.com/webui/faces/login.xhtml"
        print(f"Récupération de la page de login: {login_url}")
        
        response = session.get(login_url, timeout=30)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Page de login accessible")
            
            # Chercher des indices sur la structure du formulaire
            content = response.text.lower()
            if "loginform" in content:
                print("✅ Formulaire de login détecté")
            if "username" in content:
                print("✅ Champ username détecté")
            if "password" in content:
                print("✅ Champ password détecté")
            
            return True
        else:
            print(f"❌ Erreur d'accès: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de la simulation: {e}")
        return False

def generate_test_report():
    """Générer un rapport de test"""
    print("\n📊 RAPPORT DE DIAGNOSTIC")
    print("=" * 40)
    
    results = {}
    
    # Tests
    print("Exécution des tests...")
    results['basic_connectivity'] = test_basic_connectivity()
    results['globalroamer_connectivity'] = test_globalroamer_connectivity()
    results['credentials'] = test_credentials()
    results['login_simulation'] = simulate_login_attempt()
    
    # Résumé
    print("\n📋 RÉSUMÉ:")
    print("-" * 20)
    
    for test_name, result in results.items():
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name.replace('_', ' ').title()}: {status}")
    
    # Recommandations
    print("\n💡 RECOMMANDATIONS:")
    print("-" * 20)
    
    if not results['basic_connectivity']:
        print("- Vérifiez votre connexion internet")
        print("- Vérifiez les paramètres de proxy/firewall")
    
    if not results['globalroamer_connectivity']:
        print("- Le site GlobalRoamer pourrait être temporairement inaccessible")
        print("- Essayez plus tard ou contactez l'administrateur du site")
    
    if not results['credentials']:
        print("- Vérifiez les identifiants dans le fichier .env")
        print("- Assurez-vous qu'ils sont corrects et à jour")
    
    if not results['login_simulation']:
        print("- Le processus de login pourrait nécessiter JavaScript")
        print("- Selenium sera nécessaire pour l'automatisation complète")
    
    # Conclusion
    all_passed = all(results.values())
    if all_passed:
        print("\n🎉 TOUS LES TESTS RÉUSSIS!")
        print("Le scraper devrait fonctionner correctement.")
    else:
        print("\n⚠️ CERTAINS TESTS ONT ÉCHOUÉ")
        print("Résolvez les problèmes identifiés avant d'utiliser le scraper.")
    
    return all_passed

def main():
    """Fonction principale"""
    print("🔍 DIAGNOSTIC COMPLET DU SCRAPER GLOBALROAMER")
    print("=" * 50)
    
    success = generate_test_report()
    
    print(f"\n{'🎉 DIAGNOSTIC TERMINÉ AVEC SUCCÈS' if success else '⚠️ DIAGNOSTIC TERMINÉ AVEC DES PROBLÈMES'}")
    
    return success

if __name__ == "__main__":
    main()
