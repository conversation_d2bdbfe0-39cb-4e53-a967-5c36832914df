{"_from": "leaflet@^1.9.4", "_id": "leaflet@1.9.4", "_inBundle": false, "_integrity": "sha512-nxS1ynzJOmOlHp+iL3FyWqK89GtNL8U8rvlMOsQdTTssxZwCXh8N2NB3GDQOL+YR3XnWyZAxwQixURb+FA74PA==", "_location": "/leaflet", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "leaflet@^1.9.4", "name": "leaflet", "escapedName": "leaflet", "rawSpec": "^1.9.4", "saveSpec": null, "fetchSpec": "^1.9.4"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/leaflet/-/leaflet-1.9.4.tgz", "_shasum": "23fae724e282fa25745aff82ca4d394748db7d8d", "_spec": "leaflet@^1.9.4", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend", "bugs": {"url": "https://github.com/Leaflet/Leaflet/issues"}, "bundleDependencies": false, "deprecated": false, "description": "JavaScript library for mobile-friendly interactive maps", "devDependencies": {"@mapbox/eslint-plugin-script-tags": "^1.0.0", "@rollup/plugin-json": "^4.1.0", "bundlemon": "^1.4.0", "eslint": "^8.23.0", "eslint-config-mourner": "^2.0.3", "git-rev-sync": "^3.0.2", "happen": "~0.3.2", "husky": "^8.0.1", "karma": "^6.4.0", "karma-chrome-launcher": "^3.1.1", "karma-edge-launcher": "^0.4.2", "karma-expect": "^1.1.3", "karma-firefox-launcher": "^2.1.2", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^2.0.1", "karma-rollup-preprocessor": "^7.0.8", "karma-safari-launcher": "~1.0.0", "karma-sinon": "^1.0.5", "leafdoc": "^2.3.0", "lint-staged": "^13.0.3", "mocha": "^9.2.2", "prosthetic-hand": "^1.4.0", "rollup": "^2.78.1", "rollup-plugin-git-version": "^0.3.1", "sinon": "^7.5.0", "ssri": "^9.0.1", "uglify-js": "^3.17.0"}, "eslintConfig": {"ignorePatterns": ["dist", "debug", "docs/docs/highlight", "docs/examples/choropleth/us-states.js", "docs/examples/geojson/sample-geojson.js", "docs/examples/map-panes/eu-countries.js", "docs/examples/extending/extending-2-layers.md", "docs/_posts/2012*", "docs/_site", "build/integrity.js"], "root": true, "env": {"commonjs": true, "amd": true, "node": false}, "extends": "mourner", "plugins": ["@mapbox/eslint-plugin-script-tags"], "parserOptions": {"ecmaVersion": 6, "sourceType": "module"}, "rules": {"linebreak-style": [0, "unix"], "no-mixed-spaces-and-tabs": [2, "smart-tabs"], "indent": [2, "tab", {"VariableDeclarator": 0, "flatTernaryExpressions": true}], "curly": 2, "spaced-comment": 2, "strict": 0, "wrap-iife": 0, "key-spacing": 0, "consistent-return": 0, "no-unused-expressions": ["error", {"allowShortCircuit": true}]}, "overrides": [{"files": ["build/**/*"], "env": {"node": true}, "rules": {"global-require": 0}}, {"files": ["*.md"], "rules": {"eol-last": 0, "no-unused-vars": 0}}]}, "files": ["dist", "src", "!dist/leaflet.zip", "!*.leafdoc", "CHANGELOG.md"], "homepage": "https://leafletjs.com/", "keywords": ["gis", "map"], "license": "BSD-2-<PERSON><PERSON>", "lint-staged": {"*.(js|md)": "eslint --cache --fix"}, "main": "dist/leaflet-src.js", "name": "leaflet", "repository": {"type": "git", "url": "git://github.com/Leaflet/Leaflet.git"}, "scripts": {"build": "npm run rollup && npm run uglify", "bundlemon": "bundlemon --subProject js --defaultCompression none && bundlemon --subProject js-gzip --defaultCompression gzip", "docs": "node ./build/docs.js && node ./build/integrity.js", "lint": "eslint .", "lintfix": "npm run lint -- --fix", "prepare": "husky install", "rollup": "rollup -c build/rollup-config.js", "serve": "cd docs && bundle exec jekyll serve", "test": "karma start ./spec/karma.conf.js", "uglify": "uglifyjs dist/leaflet-src.js -c -m -o dist/leaflet.js --source-map filename=dist/leaflet.js.map --source-map content=dist/leaflet-src.js.map --source-map url=leaflet.js.map --comments", "watch": "rollup -w -c build/rollup-config.js"}, "style": "dist/leaflet.css", "version": "1.9.4"}