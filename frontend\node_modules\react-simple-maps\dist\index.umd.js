!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("prop-types"),require("d3-geo"),require("topojson-client"),require("d3-zoom"),require("d3-selection")):"function"==typeof define&&define.amd?define(["exports","react","prop-types","d3-geo","topojson-client","d3-zoom","d3-selection"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).reactSimpleMaps=e.reactSimpleMaps||{},e.<PERSON>,e.PropTypes,e.d3,e.topojson,e.d3,e.d3)}(this,(function(e,t,r,o,n,a,u){"use strict";function s(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function l(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var i=s(t),c=s(r),f=l(o);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach((function(t){y(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function y(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function g(){return g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},g.apply(this,arguments)}function v(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r,o,n={},a=Object.keys(e);for(o=0;o<a.length;o++)r=a[o],t.indexOf(r)>=0||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function h(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var o,n,a=[],u=!0,s=!1;try{for(r=r.call(e);!(u=(o=r.next()).done)&&(a.push(o.value),!t||a.length!==t);u=!0);}catch(e){s=!0,n=e}finally{try{u||null==r.return||r.return()}finally{if(s)throw n}}return a}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return b(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,o=new Array(t);r<t;r++)o[r]=e[r];return o}var j=["width","height","projection","projectionConfig"],M=f.geoPath,E=v(f,["geoPath"]),x=t.createContext(),k=function(e){var r=e.width,o=e.height,n=e.projection,a=e.projectionConfig,u=v(e,j),s=h(a.center||[],2),l=s[0],c=s[1],f=h(a.rotate||[],3),d=f[0],p=f[1],m=f[2],y=h(a.parallels||[],2),b=y[0],k=y[1],w=a.scale||null,O=t.useMemo((function(){return function(e){var t=e.projectionConfig,r=void 0===t?{}:t,o=e.projection,n=void 0===o?"geoEqualEarth":o,a=e.width,u=void 0===a?800:a,s=e.height,l=void 0===s?600:s;if("function"==typeof n)return n;var i=E[n]().translate([u/2,l/2]);return[i.center?"center":null,i.rotate?"rotate":null,i.scale?"scale":null,i.parallels?"parallels":null].forEach((function(e){e&&(i=i[e](r[e]||i[e]()))})),i}({projectionConfig:{center:l||0===l||c||0===c?[l,c]:null,rotate:d||0===d||p||0===p?[d,p,m]:null,parallels:b||0===b||k||0===k?[b,k]:null,scale:w},projection:n,width:r,height:o})}),[r,o,n,l,c,d,p,m,b,k,w]),N=t.useCallback(O,[O]),S=t.useMemo((function(){return{width:r,height:o,projection:N,path:M().projection(N)}}),[r,o,N]);return i.default.createElement(x.Provider,g({value:S},u))};k.propTypes={width:c.default.number,height:c.default.number,projection:c.default.oneOfType([c.default.string,c.default.func]),projectionConfig:c.default.object};var w=["width","height","projection","projectionConfig","className"],O=t.forwardRef((function(e,t){var r=e.width,o=void 0===r?800:r,n=e.height,a=void 0===n?600:n,u=e.projection,s=void 0===u?"geoEqualEarth":u,l=e.projectionConfig,c=void 0===l?{}:l,f=e.className,d=void 0===f?"":f,p=v(e,w);return i.default.createElement(k,{width:o,height:a,projection:s,projectionConfig:c},i.default.createElement("svg",g({ref:t,viewBox:"0 0 ".concat(o," ").concat(a),className:"rsm-svg ".concat(d)},p)))}));function N(e,t,r){var o=(e*r.k-e)/2,n=(t*r.k-t)/2;return[e/2-(o+r.x)/r.k,t/2-(n+r.y)/r.k]}function S(e,t){if(!("Topology"===e.type))return t?t(e.features||e):e.features||e;var r=n.feature(e,e.objects[Object.keys(e.objects)[0]]).features;return t?t(r):r}function P(e){return"Topology"===e.type?{outline:n.mesh(e,e.objects[Object.keys(e.objects)[0]],(function(e,t){return e===t})),borders:n.mesh(e,e.objects[Object.keys(e.objects)[0]],(function(e,t){return e!==t}))}:null}function C(e,t){return e?e.map((function(e,r){return p(p({},e),{},{rsmKey:"geo-".concat(r),svgPath:t(e)})})):[]}function T(e){var r=e.geography,o=e.parseGeographies,n=t.useContext(x).path,a=h(t.useState({}),2),u=a[0],s=a[1];t.useEffect((function(){var e;"undefined"!==("undefined"==typeof window?"undefined":m(window))&&(r&&("string"==typeof r?(e=r,fetch(e).then((function(e){if(!e.ok)throw Error(e.statusText);return e.json()})).catch((function(e){console.log("There was a problem when fetching the data: ",e)}))).then((function(e){e&&s({geographies:S(e,o),mesh:P(e)})})):s({geographies:S(r,o),mesh:P(r)})))}),[r,o]);var l=t.useMemo((function(){var e=u.mesh||{},t=function(e,t,r){return e&&t?{outline:p(p({},e),{},{rsmKey:"outline",svgPath:r(e)}),borders:p(p({},t),{},{rsmKey:"borders",svgPath:r(t)})}:{}}(e.outline,e.borders,n);return{geographies:C(u.geographies,n),outline:t.outline,borders:t.borders}}),[u,n]);return{geographies:l.geographies,outline:l.outline,borders:l.borders}}O.displayName="ComposableMap",O.propTypes={width:c.default.number,height:c.default.number,projection:c.default.oneOfType([c.default.string,c.default.func]),projectionConfig:c.default.object,className:c.default.string};var R=["geography","children","parseGeographies","className"],Z=t.forwardRef((function(e,r){var o=e.geography,n=e.children,a=e.parseGeographies,u=e.className,s=void 0===u?"":u,l=v(e,R),c=t.useContext(x),f=c.path,d=c.projection,p=T({geography:o,parseGeographies:a}),m=p.geographies,y=p.outline,h=p.borders;return i.default.createElement("g",g({ref:r,className:"rsm-geographies ".concat(s)},l),m&&m.length>0&&n({geographies:m,outline:y,borders:h,path:f,projection:d}))}));Z.displayName="Geographies",Z.propTypes={geography:c.default.oneOfType([c.default.string,c.default.object,c.default.array]),children:c.default.func,parseGeographies:c.default.func,className:c.default.string};var z=["geography","onMouseEnter","onMouseLeave","onMouseDown","onMouseUp","onFocus","onBlur","style","className"],G=t.forwardRef((function(e,r){var o=e.geography,n=e.onMouseEnter,a=e.onMouseLeave,u=e.onMouseDown,s=e.onMouseUp,l=e.onFocus,c=e.onBlur,f=e.style,d=void 0===f?{}:f,p=e.className,m=void 0===p?"":p,y=v(e,z),b=h(t.useState(!1),2),j=b[0],M=b[1],E=h(t.useState(!1),2),x=E[0],k=E[1];return i.default.createElement("path",g({ref:r,tabIndex:"0",className:"rsm-geography ".concat(m),d:o.svgPath,onMouseEnter:function(e){k(!0),n&&n(e)},onMouseLeave:function(e){k(!1),j&&M(!1),a&&a(e)},onFocus:function(e){k(!0),l&&l(e)},onBlur:function(e){k(!1),j&&M(!1),c&&c(e)},onMouseDown:function(e){M(!0),u&&u(e)},onMouseUp:function(e){M(!1),s&&s(e)},style:d[j||x?j?"pressed":"hover":"default"]},y))}));G.displayName="Geography",G.propTypes={geography:c.default.object,onMouseEnter:c.default.func,onMouseLeave:c.default.func,onMouseDown:c.default.func,onMouseUp:c.default.func,onFocus:c.default.func,onBlur:c.default.func,style:c.default.object,className:c.default.string};var D=t.memo(G),L=["fill","stroke","step","className"],A=t.forwardRef((function(e,r){var n=e.fill,a=void 0===n?"transparent":n,u=e.stroke,s=void 0===u?"currentcolor":u,l=e.step,c=void 0===l?[10,10]:l,f=e.className,d=void 0===f?"":f,p=v(e,L),m=t.useContext(x).path;return i.default.createElement("path",g({ref:r,d:m(o.geoGraticule().step(c)()),fill:a,stroke:s,className:"rsm-graticule ".concat(d)},p))}));A.displayName="Graticule",A.propTypes={fill:c.default.string,stroke:c.default.string,step:c.default.array,className:c.default.string};var B=t.memo(A),F=["value"],U=t.createContext(),q={x:0,y:0,k:1,transformString:"translate(0 0) scale(1)"},W=function(e){var t=e.value,r=void 0===t?q:t,o=v(e,F);return i.default.createElement(U.Provider,g({value:r},o))};W.propTypes={x:c.default.number,y:c.default.number,k:c.default.number,transformString:c.default.string};function I(e){var r=e.center,o=e.filterZoomEvent,n=e.onMoveStart,s=e.onMoveEnd,l=e.onMove,i=e.translateExtent,c=void 0===i?[[-1/0,-1/0],[1/0,1/0]]:i,f=e.scaleExtent,d=void 0===f?[1,8]:f,p=e.zoom,m=void 0===p?1:p,y=t.useContext(x),g=y.width,v=y.height,b=y.projection,j=h(r,2),M=j[0],E=j[1],k=h(t.useState({x:0,y:0,k:1}),2),w=k[0],O=k[1],S=t.useRef({x:0,y:0,k:1}),P=t.useRef(),C=t.useRef(),T=t.useRef(!1),R=h(c,2),Z=R[0],z=R[1],G=h(Z,2),D=G[0],L=G[1],A=h(z,2),B=A[0],F=A[1],U=h(d,2),q=U[0],W=U[1];return t.useEffect((function(){var e=u.select(P.current);var t=a.zoom().filter((function(e){return o?o(e):!!e&&(!e.ctrlKey&&!e.button)})).scaleExtent([q,W]).translateExtent([[D,L],[B,F]]).on("start",(function(e){n&&!T.current&&n({coordinates:b.invert(N(g,v,e.transform)),zoom:e.transform.k},e)})).on("zoom",(function(e){if(!T.current){var t=e.transform,r=e.sourceEvent;O({x:t.x,y:t.y,k:t.k,dragging:r}),l&&l({x:t.x,y:t.y,zoom:t.k,dragging:r},e)}})).on("end",(function(e){if(T.current)T.current=!1;else{var t=h(b.invert(N(g,v,e.transform)),2),r=t[0],o=t[1];S.current={x:r,y:o,k:e.transform.k},s&&s({coordinates:[r,o],zoom:e.transform.k},e)}}));C.current=t,e.call(t)}),[g,v,D,L,B,F,q,W,b,n,l,s,o]),t.useEffect((function(){if(M!==S.current.x||E!==S.current.y||m!==S.current.k){var e=b([M,E]),t=e[0]*m,r=e[1]*m,o=u.select(P.current);T.current=!0,o.call(C.current.transform,a.zoomIdentity.translate(g/2-t,v/2-r).scale(m)),O({x:g/2-t,y:v/2-r,k:m}),S.current={x:M,y:E,k:m}}}),[M,E,m,g,v,b]),{mapRef:P,position:w,transformString:"translate(".concat(w.x," ").concat(w.y,") scale(").concat(w.k,")")}}var K=["center","zoom","minZoom","maxZoom","translateExtent","filterZoomEvent","onMoveStart","onMove","onMoveEnd","className"],_=t.forwardRef((function(e,r){var o=e.center,n=void 0===o?[0,0]:o,a=e.zoom,u=void 0===a?1:a,s=e.minZoom,l=void 0===s?1:s,c=e.maxZoom,f=void 0===c?8:c,d=e.translateExtent,p=e.filterZoomEvent,m=e.onMoveStart,y=e.onMove,h=e.onMoveEnd,b=e.className,j=v(e,K),M=t.useContext(x),E=M.width,k=M.height,w=I({center:n,filterZoomEvent:p,onMoveStart:m,onMove:y,onMoveEnd:h,scaleExtent:[l,f],translateExtent:d,zoom:u}),O=w.mapRef,N=w.transformString,S=w.position;return i.default.createElement(W,{value:{x:S.x,y:S.y,k:S.k,transformString:N}},i.default.createElement("g",{ref:O},i.default.createElement("rect",{width:E,height:k,fill:"transparent"}),i.default.createElement("g",g({ref:r,transform:N,className:"rsm-zoomable-group ".concat(b)},j))))}));_.displayName="ZoomableGroup",_.propTypes={center:c.default.array,zoom:c.default.number,minZoom:c.default.number,maxZoom:c.default.number,translateExtent:c.default.arrayOf(c.default.array),onMoveStart:c.default.func,onMove:c.default.func,onMoveEnd:c.default.func,className:c.default.string};var Q=["id","fill","stroke","strokeWidth","className"],$=t.forwardRef((function(e,r){var o=e.id,n=void 0===o?"rsm-sphere":o,a=e.fill,u=void 0===a?"transparent":a,s=e.stroke,l=void 0===s?"currentcolor":s,c=e.strokeWidth,f=void 0===c?.5:c,d=e.className,p=void 0===d?"":d,m=v(e,Q),y=t.useContext(x).path,h=t.useMemo((function(){return y({type:"Sphere"})}),[y]);return i.default.createElement(t.Fragment,null,i.default.createElement("defs",null,i.default.createElement("clipPath",{id:n},i.default.createElement("path",{d:h}))),i.default.createElement("path",g({ref:r,d:h,fill:u,stroke:l,strokeWidth:f,style:{pointerEvents:"none"},className:"rsm-sphere ".concat(p)},m)))}));$.displayName="Sphere",$.propTypes={id:c.default.string,fill:c.default.string,stroke:c.default.string,strokeWidth:c.default.number,className:c.default.string};var H=t.memo($),J=["coordinates","children","onMouseEnter","onMouseLeave","onMouseDown","onMouseUp","onFocus","onBlur","style","className"],V=t.forwardRef((function(e,r){var o=e.coordinates,n=e.children,a=e.onMouseEnter,u=e.onMouseLeave,s=e.onMouseDown,l=e.onMouseUp,c=e.onFocus,f=e.onBlur,d=e.style,p=void 0===d?{}:d,m=e.className,y=void 0===m?"":m,b=v(e,J),j=t.useContext(x).projection,M=h(t.useState(!1),2),E=M[0],k=M[1],w=h(t.useState(!1),2),O=w[0],N=w[1],S=h(j(o),2),P=S[0],C=S[1];return i.default.createElement("g",g({ref:r,transform:"translate(".concat(P,", ").concat(C,")"),className:"rsm-marker ".concat(y),onMouseEnter:function(e){N(!0),a&&a(e)},onMouseLeave:function(e){N(!1),E&&k(!1),u&&u(e)},onFocus:function(e){N(!0),c&&c(e)},onBlur:function(e){N(!1),E&&k(!1),f&&f(e)},onMouseDown:function(e){k(!0),s&&s(e)},onMouseUp:function(e){k(!1),l&&l(e)},style:p[E||O?E?"pressed":"hover":"default"]},b),n)}));V.displayName="Marker",V.propTypes={coordinates:c.default.array,children:c.default.oneOfType([c.default.node,c.default.arrayOf(c.default.node)]),onMouseEnter:c.default.func,onMouseLeave:c.default.func,onMouseDown:c.default.func,onMouseUp:c.default.func,onFocus:c.default.func,onBlur:c.default.func,style:c.default.object,className:c.default.string};var X=["from","to","coordinates","stroke","strokeWidth","fill","className"],Y=t.forwardRef((function(e,r){var o=e.from,n=void 0===o?[0,0]:o,a=e.to,u=void 0===a?[0,0]:a,s=e.coordinates,l=e.stroke,c=void 0===l?"currentcolor":l,f=e.strokeWidth,d=void 0===f?3:f,p=e.fill,m=void 0===p?"transparent":p,y=e.className,h=void 0===y?"":y,b=v(e,X),j=t.useContext(x).path,M={type:"LineString",coordinates:s||[n,u]};return i.default.createElement("path",g({ref:r,d:j(M),className:"rsm-line ".concat(h),stroke:c,strokeWidth:d,fill:m},b))}));Y.displayName="Line",Y.propTypes={from:c.default.array,to:c.default.array,coordinates:c.default.array,stroke:c.default.string,strokeWidth:c.default.number,fill:c.default.string,className:c.default.string};var ee=["subject","children","connectorProps","dx","dy","curve","className"],te=t.forwardRef((function(e,r){var o=e.subject,n=e.children,a=e.connectorProps,u=e.dx,s=void 0===u?30:u,l=e.dy,c=void 0===l?30:l,f=e.curve,d=void 0===f?0:f,p=e.className,m=void 0===p?"":p,y=v(e,ee),b=h((0,t.useContext(x).projection)(o),2),j=b[0],M=b[1],E=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.5,o=Array.isArray(r)?r:[r,r],n=e/2*o[0],a=t/2*o[1];return"M".concat(0,",",0," Q",-e/2-n,",").concat(-t/2+a," ").concat(-e,",").concat(-t)}(s,c,d);return i.default.createElement("g",g({ref:r,transform:"translate(".concat(j+s,", ").concat(M+c,")"),className:"rsm-annotation ".concat(m)},y),i.default.createElement("path",g({d:E,fill:"transparent",stroke:"#000"},a)),n)}));te.displayName="Annotation",te.propTypes={subject:c.default.array,children:c.default.oneOfType([c.default.node,c.default.arrayOf(c.default.node)]),dx:c.default.number,dy:c.default.number,curve:c.default.number,connectorProps:c.default.object,className:c.default.string},e.Annotation=te,e.ComposableMap=O,e.Geographies=Z,e.Geography=D,e.Graticule=B,e.Line=Y,e.MapContext=x,e.MapProvider=k,e.Marker=V,e.Sphere=H,e.ZoomPanContext=U,e.ZoomPanProvider=W,e.ZoomableGroup=_,e.useGeographies=T,e.useMapContext=function(){return t.useContext(x)},e.useZoomPan=I,e.useZoomPanContext=function(){return t.useContext(U)},Object.defineProperty(e,"__esModule",{value:!0})}));
