@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Variables personnalisées */
:root {
  --color-primary: #0ea5e9;
  --color-primary-light: #7dd3fc;
  --color-primary-dark: #0369a1;
  --color-secondary: #8b5cf6;
  --color-secondary-light: #c4b5fd;
  --color-secondary-dark: #6d28d9;
  --color-success: #10b981;
  --color-error: #ef4444;
  --color-warning: #f59e0b;
  --color-info: #3b82f6;
}

html {
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  @apply bg-gray-50;
}

/* Styles personnalisés */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600 focus:ring-2 focus:ring-primary-300;
}

  .btn-secondary {
    @apply bg-secondary-500 text-white hover:bg-secondary-600 focus:ring-2 focus:ring-secondary-300;
}

  .btn-outline {
    @apply border border-gray-300 text-gray-700 bg-white hover:bg-gray-50;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
}

/* Ajout d'un style pour masquer la section "Analyse des Échecs" */
.analyse-echecs {
  display: none !important;
}

/* Masquer également le conteneur avec les indicateurs 0%, Données non disponib..., etc. */
.echecs-indicators {
  display: none !important;
}

/* Styles pour Leaflet */
.leaflet-container {
  width: 100%;
  height: 100%;
  z-index: 1;
}

.leaflet-popup-content-wrapper {
  background: white;
  color: #333;
  border-radius: 4px;
  padding: 1px;
  box-shadow: 0 3px 14px rgba(0,0,0,0.4);
}

.leaflet-popup-content {
  margin: 13px 19px;
  line-height: 1.4;
}

.leaflet-popup-tip-container {
  width: 40px;
  height: 20px;
  position: absolute;
  left: 50%;
  margin-left: -20px;
  overflow: hidden;
  pointer-events: none;
}

.leaflet-popup-tip {
  background: white;
  box-shadow: 0 3px 14px rgba(0,0,0,0.4);
}
