{"_from": "d3-zoom@^2.0.0", "_id": "d3-zoom@2.0.0", "_inBundle": false, "_integrity": "sha512-fFg7aoaEm9/jf+qfstak0IYpnesZLiMX6GZvXtUSdv8RH2o4E2qeelgdU09eKS6wGuiGMfcnMI0nTIqWzRHGpw==", "_location": "/d3-zoom", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-zoom@^2.0.0", "name": "d3-zoom", "escapedName": "d3-zoom", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/react-simple-maps"], "_resolved": "https://registry.npmjs.org/d3-zoom/-/d3-zoom-2.0.0.tgz", "_shasum": "f04d0afd05518becce879d04709c47ecd93fba54", "_spec": "d3-zoom@^2.0.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\react-simple-maps", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-zoom/issues"}, "bundleDependencies": false, "dependencies": {"d3-dispatch": "1 - 2", "d3-drag": "2", "d3-interpolate": "1 - 2", "d3-selection": "2", "d3-transition": "2"}, "deprecated": false, "description": "Pan and zoom SVG, HTML or Canvas using mouse or touch input.", "devDependencies": {"eslint": "6", "jsdom": "^16.2.2", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-zoom/", "jsdelivr": "dist/d3-zoom.min.js", "keywords": ["d3", "d3-module", "zoom", "behavior", "interaction"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-zoom.js", "module": "src/index.js", "name": "d3-zoom", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-zoom.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src"}, "unpkg": "dist/d3-zoom.min.js", "version": "2.0.0"}