// https://github.com/mbostock/internmap/ v1.0.1 Copyright 2021 Mike <PERSON>
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).internmap={})}(this,(function(e){"use strict";class t extends Map{constructor(e,t=i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const[t,n]of e)this.set(t,n)}get(e){return super.get(s(this,e))}has(e){return super.has(s(this,e))}set(e,t){return super.set(r(this,e),t)}delete(e){return super.delete(u(this,e))}}class n extends Set{constructor(e,t=i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const t of e)this.add(t)}has(e){return super.has(s(this,e))}add(e){return super.add(r(this,e))}delete(e){return super.delete(u(this,e))}}function s({_intern:e,_key:t},n){const s=t(n);return e.has(s)?e.get(s):n}function r({_intern:e,_key:t},n){const s=t(n);return e.has(s)?e.get(s):(e.set(s,n),n)}function u({_intern:e,_key:t},n){const s=t(n);return e.has(s)&&(n=e.get(n),e.delete(s)),n}function i(e){return null!==e&&"object"==typeof e?e.valueOf():e}e.InternMap=t,e.InternSet=n,Object.defineProperty(e,"__esModule",{value:!0})}));
