import React from 'react';
// import WorldMap from '../components/WorldMap'; // Temporarily disabled due to Context issues

const mockKpiData = [
  { countryCode: 'FR', attachmentRate: 95 },
  { countryCode: 'US', attachmentRate: 88 },
  { countryCode: 'DE', attachmentRate: 92 },
  { countryCode: 'GB', attachmentRate: 85 },
  { countryCode: 'ES', attachmentRate: 78 },
  { countryCode: 'IT', attachmentRate: 82 },
  { countryCode: 'MA', attachmentRate: 75 },
  { countryCode: 'DZ', attachmentRate: 65 },
  { countryCode: 'TN', attachmentRate: 70 },
  { countryCode: 'SN', attachmentRate: 60 }
];

const WorldMapPage: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold mb-6">World Map - Country Attachment Rates</h1>
        <div className="mb-4">
          <div className="flex items-center justify-end space-x-4">
            <div className="flex items-center">
              <div className="w-4 h-4 bg-[#2ecc71] rounded mr-2" />
              <span>≥ 90%</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-[#f1c40f] rounded mr-2" />
              <span>70-89%</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-[#e74c3c] rounded mr-2" />
              <span>&lt; 70%</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-[#95a5a6] rounded mr-2" />
              <span>No data</span>
            </div>
          </div>
        </div>
        <div className="relative">
          <WorldMapFixed kpiData={mockKpiData} />
        </div>
      </div>
    </div>
  );
};

export default WorldMapPage; 
