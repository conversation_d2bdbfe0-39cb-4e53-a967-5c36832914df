import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import React, { useEffect, useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSON } from 'react-leaflet';

// Delete default icon's URL from internal object
delete (L.Icon.Default.prototype as any)._getIconUrl;

// Configure default icon
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface KpiData {
  countryCode: string;
  attachmentRate: number;
}

interface WorldMapFixedProps {
  kpiData?: KpiData[];
}

const WorldMapFixed: React.FC<WorldMapFixedProps> = ({ kpiData = [] }) => {
  const [geoJsonData, setGeoJsonData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadGeoJson = async () => {
      try {
        setIsLoading(true);
        const data = await import('../assets/custom.geo.json');
        setGeoJsonData(data.default);
        setError(null);
      } catch (error) {
        console.error('Error loading GeoJSON:', error);
        setError('Failed to load map data');
      } finally {
        setIsLoading(false);
      }
    };

    loadGeoJson();
  }, []);

  const getCountryColor = useCallback((countryCode: string): string => {
    const countryData = kpiData.find(item => item.countryCode === countryCode);
    if (!countryData) return '#95a5a6'; // Gray for no data
    
    const rate = countryData.attachmentRate;
    if (rate >= 90) return '#2ecc71'; // Green
    if (rate >= 70) return '#f1c40f'; // Yellow
    return '#e74c3c'; // Red
  }, [kpiData]);

  const onEachFeature = useCallback((feature: any, layer: L.Layer) => {
    const countryCode = feature.properties?.ISO_A2;
    const countryName = feature.properties?.NAME;
    
    if (countryCode && countryName) {
      const countryData = kpiData.find(item => item.countryCode === countryCode);
      const attachmentRate = countryData?.attachmentRate || 'No data';
      
      layer.bindPopup(`
        <div style="font-family: Arial, sans-serif;">
          <h3 style="margin: 0 0 8px 0; color: #333;">${countryName}</h3>
          <p style="margin: 0; color: #666;">
            <strong>Attachment Rate:</strong> ${attachmentRate}${typeof attachmentRate === 'number' ? '%' : ''}
          </p>
        </div>
      `);
    }
  }, [kpiData]);

  const geoJsonStyle = useCallback((feature: any) => {
    const countryCode = feature?.properties?.ISO_A2;
    return {
      fillColor: getCountryColor(countryCode),
      weight: 1,
      opacity: 1,
      color: '#ffffff',
      fillOpacity: 0.7
    };
  }, [getCountryColor]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-[600px] w-full bg-gray-100 border border-gray-300 rounded">
        <div className="text-center">
          <p className="text-red-600 mb-2">{error}</p>
          <button
            onClick={() => {
              setError(null);
              setIsLoading(true);
              // Retry loading
              import('../assets/custom.geo.json')
                .then(data => {
                  setGeoJsonData(data.default);
                  setError(null);
                })
                .catch(err => {
                  console.error('Retry failed:', err);
                  setError('Failed to load map data');
                })
                .finally(() => setIsLoading(false));
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[600px] w-full bg-gray-50 border border-gray-300 rounded">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading world map...</p>
        </div>
      </div>
    );
  }

  if (!geoJsonData) {
    return (
      <div className="flex items-center justify-center h-[600px] w-full bg-gray-100 border border-gray-300 rounded">
        <div className="text-gray-600">No map data available</div>
      </div>
    );
  }

  return (
    <div className="relative h-[600px] w-full">
      <MapContainer
        center={[20, 0]}
        zoom={2}
        className="h-full w-full"
        scrollWheelZoom={true}
        style={{ height: '100%', width: '100%' }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        <GeoJSON
          data={geoJsonData}
          style={geoJsonStyle}
          onEachFeature={onEachFeature}
        />
      </MapContainer>
    </div>
  );
};

export default WorldMapFixed;
