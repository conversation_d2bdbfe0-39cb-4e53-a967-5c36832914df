import React, { useEffect, useState } from 'react';
import { FaBrain, FaCheckCircle, FaClock, FaDatabase, FaExclamationCircle, FaPlay } from 'react-icons/fa';

interface TrainingStatus {
  model_exists: boolean;
  model_trained: boolean;
  model_path: string;
  last_training?: {
    timestamp: string;
    training_data_size: number;
    test_data_size: number;
    training_time_seconds: number;
    anomalies_detected: number;
    evaluation_metrics: {
      anomaly_rate_percent: number;
      consensus_rate_percent: number;
    };
  };
}

interface TrainingResult {
  status: string;
  message: string;
  training_summary?: any;
}

const MLTrainingPanel: React.FC = () => {
  const [status, setStatus] = useState<TrainingStatus | null>(null);
  const [isTraining, setIsTraining] = useState(false);
  const [trainingResult, setTrainingResult] = useState<TrainingResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Paramètres d'entraînement
  const [daysBack, setDaysBack] = useState(90);
  const [testSize, setTestSize] = useState(0.2);
  const [forceRetrain, setForceRetrain] = useState(false);

  useEffect(() => {
    fetchModelStatus();
  }, []);

  const fetchModelStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/kpi/model/status');
      if (response.ok) {
        const data = await response.json();
        setStatus(data);
      } else {
        setError('Erreur lors de la récupération du statut');
      }
    } catch (err) {
      setError('Erreur de connexion');
    } finally {
      setLoading(false);
    }
  };

  const startTraining = async () => {
    try {
      setIsTraining(true);
      setTrainingResult(null);
      setError(null);

      const response = await fetch('/api/kpi/train', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          days_back: daysBack,
          test_size: testSize,
          force_retrain: forceRetrain,
        }),
      });

      const result = await response.json();
      
      if (response.ok) {
        setTrainingResult(result);
        // Rafraîchir le statut après l'entraînement
        await fetchModelStatus();
      } else {
        setError(result.detail || 'Erreur lors de l\'entraînement');
      }
    } catch (err) {
      setError('Erreur de connexion lors de l\'entraînement');
    } finally {
      setIsTraining(false);
    }
  };

  const analyzeKPIs = async () => {
    try {
      const response = await fetch('/api/kpi/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          days_to_analyze: 1,
        }),
      });

      const result = await response.json();
      
      if (response.ok) {
        alert(`Analyse terminée: ${result.anomalies.length} anomalies détectées`);
      } else {
        setError(result.detail || 'Erreur lors de l\'analyse');
      }
    } catch (err) {
      setError('Erreur lors de l\'analyse');
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600">Chargement du statut du modèle...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex items-center mb-6">
        <FaBrain className="h-6 w-6 text-blue-600 mr-2" />
        <h2 className="text-xl font-bold text-gray-800">Entraînement du Modèle ML</h2>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <FaExclamationCircle className="h-5 w-5 text-red-500 mr-2" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Statut du modèle */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">Statut du Modèle</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            {status?.model_exists ? (
              <FaCheckCircle className="h-5 w-5 text-green-500 mr-2" />
            ) : (
              <FaExclamationCircle className="h-5 w-5 text-red-500 mr-2" />
            )}
            <span className={status?.model_exists ? 'text-green-700' : 'text-red-700'}>
              {status?.model_exists ? 'Modèle existant' : 'Aucun modèle'}
            </span>
          </div>
          
          <div className="flex items-center">
            {status?.model_trained ? (
              <FaCheckCircle className="h-5 w-5 text-green-500 mr-2" />
            ) : (
              <FaClock className="h-5 w-5 text-yellow-500 mr-2" />
            )}
            <span className={status?.model_trained ? 'text-green-700' : 'text-yellow-700'}>
              {status?.model_trained ? 'Entraîné' : 'Non entraîné'}
            </span>
          </div>

          <div className="flex items-center">
            <FaDatabase className="h-5 w-5 text-blue-500 mr-2" />
            <span className="text-gray-700">
              {status?.model_path || 'Chemin non défini'}
            </span>
          </div>
        </div>

        {/* Informations du dernier entraînement */}
        {status?.last_training && (
          <div className="mt-4 p-3 bg-blue-50 rounded">
            <h4 className="font-medium text-blue-800 mb-2">Dernier entraînement</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
              <div>
                <span className="text-gray-600">Date:</span>
                <div className="font-medium">
                  {new Date(status.last_training.timestamp).toLocaleDateString()}
                </div>
              </div>
              <div>
                <span className="text-gray-600">Données:</span>
                <div className="font-medium">
                  {status.last_training.training_data_size.toLocaleString()}
                </div>
              </div>
              <div>
                <span className="text-gray-600">Durée:</span>
                <div className="font-medium">
                  {status.last_training.training_time_seconds.toFixed(1)}s
                </div>
              </div>
              <div>
                <span className="text-gray-600">Anomalies:</span>
                <div className="font-medium">
                  {status.last_training.anomalies_detected}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Paramètres d'entraînement */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-3">Paramètres d'entraînement</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Jours de données historiques
            </label>
            <input
              type="number"
              value={daysBack}
              onChange={(e) => setDaysBack(parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              min="7"
              max="365"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Proportion de test (0.1-0.5)
            </label>
            <input
              type="number"
              value={testSize}
              onChange={(e) => setTestSize(parseFloat(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              min="0.1"
              max="0.5"
              step="0.1"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="forceRetrain"
              checked={forceRetrain}
              onChange={(e) => setForceRetrain(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="forceRetrain" className="ml-2 text-sm text-gray-700">
              Forcer le réentraînement
            </label>
          </div>
        </div>
      </div>

      {/* Boutons d'action */}
      <div className="flex space-x-4">
        <button
          onClick={startTraining}
          disabled={isTraining}
          className={`flex items-center px-6 py-3 rounded-lg font-medium ${
            isTraining
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          {isTraining ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Entraînement en cours...
            </>
          ) : (
            <>
              <FaPlay className="h-4 w-4 mr-2" />
              Démarrer l'entraînement
            </>
          )}
        </button>

        {status?.model_exists && (
          <button
            onClick={analyzeKPIs}
            className="flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium"
          >
            <FaBrain className="h-4 w-4 mr-2" />
            Analyser les KPIs
          </button>
        )}
      </div>

      {/* Résultats d'entraînement */}
      {trainingResult && (
        <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-800 mb-2">
            Résultats d'entraînement
          </h3>
          <p className="text-green-700 mb-2">{trainingResult.message}</p>
          
          {trainingResult.training_summary && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Données d'entraînement:</span>
                <div className="font-medium">
                  {trainingResult.training_summary.training_data_size?.toLocaleString()}
                </div>
              </div>
              <div>
                <span className="text-gray-600">Données de test:</span>
                <div className="font-medium">
                  {trainingResult.training_summary.test_data_size?.toLocaleString()}
                </div>
              </div>
              <div>
                <span className="text-gray-600">Temps d'entraînement:</span>
                <div className="font-medium">
                  {trainingResult.training_summary.training_time_seconds?.toFixed(1)}s
                </div>
              </div>
              <div>
                <span className="text-gray-600">Anomalies détectées:</span>
                <div className="font-medium">
                  {trainingResult.training_summary.anomalies_detected}
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MLTrainingPanel;
