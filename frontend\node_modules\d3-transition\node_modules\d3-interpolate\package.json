{"_from": "d3-interpolate@1 - 2", "_id": "d3-interpolate@2.0.1", "_inBundle": false, "_integrity": "sha512-c5UhwwTs/yybcmTpAVqwSFl6vrQ8JZJoT5F7xNFK9pymv5C0Ymcc9/LIJHtYIggg/yS9YHw8i8O8tgb9pupjeQ==", "_location": "/d3-transition/d3-interpolate", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-interpolate@1 - 2", "name": "d3-interpolate", "escapedName": "d3-interpolate", "rawSpec": "1 - 2", "saveSpec": null, "fetchSpec": "1 - 2"}, "_requiredBy": ["/d3-transition"], "_resolved": "https://registry.npmjs.org/d3-interpolate/-/d3-interpolate-2.0.1.tgz", "_shasum": "98be499cfb8a3b94d4ff616900501a64abc91163", "_spec": "d3-interpolate@1 - 2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\d3-transition", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-interpolate/issues"}, "bundleDependencies": false, "dependencies": {"d3-color": "1 - 2"}, "deprecated": false, "description": "Interpolate numbers, colors, strings, arrays, objects, whatever!", "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-interpolate/", "jsdelivr": "dist/d3-interpolate.min.js", "keywords": ["d3", "d3-module", "interpolate", "interpolation", "color"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-interpolate.js", "module": "src/index.js", "name": "d3-interpolate", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-interpolate.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src"}, "sideEffects": false, "unpkg": "dist/d3-interpolate.min.js", "version": "2.0.1"}