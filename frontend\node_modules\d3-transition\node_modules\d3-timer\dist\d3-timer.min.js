// https://d3js.org/d3-timer/ v2.0.0 Copyright 2020 Mike <PERSON>
!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t=t||self).d3=t.d3||{})}(this,function(t){"use strict";var n,e,o=0,r=0,i=0,u=1e3,l=0,a=0,c=0,s="object"==typeof performance&&performance.now?performance:Date,f="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function _(){return a||(f(m),a=s.now()+c)}function m(){a=0}function p(){this._call=this._time=this._next=null}function w(t,n,e){var o=new p;return o.restart(t,n,e),o}function d(){_(),++o;for(var t,e=n;e;)(t=a-e._time)>=0&&e._call.call(null,t),e=e._next;--o}function h(){a=(l=s.now())+c,o=r=0;try{d()}finally{o=0,function(){var t,o,r=n,i=1/0;for(;r;)r._call?(i>r._time&&(i=r._time),t=r,r=r._next):(o=r._next,r._next=null,r=t?t._next=o:n=o);e=t,v(i)}(),a=0}}function y(){var t=s.now(),n=t-l;n>u&&(c-=n,l=t)}function v(t){o||(r&&(r=clearTimeout(r)),t-a>24?(t<1/0&&(r=setTimeout(h,t-s.now()-c)),i&&(i=clearInterval(i))):(i||(l=s.now(),i=setInterval(y,u)),o=1,f(h)))}p.prototype=w.prototype={constructor:p,restart:function(t,o,r){if("function"!=typeof t)throw new TypeError("callback is not a function");r=(null==r?_():+r)+(null==o?0:+o),this._next||e===this||(e?e._next=this:n=this,e=this),this._call=t,this._time=r,v()},stop:function(){this._call&&(this._call=null,this._time=1/0,v())}},t.interval=function(t,n,e){var o=new p,r=n;return null==n?(o.restart(t,n,e),o):(o._restart=o.restart,o.restart=function(t,n,e){n=+n,e=null==e?_():+e,o._restart(function i(u){u+=r,o._restart(i,r+=n,e),t(u)},n,e)},o.restart(t,n,e),o)},t.now=_,t.timeout=function(t,n,e){var o=new p;return n=null==n?0:+n,o.restart(e=>{o.stop(),t(e+n)},n,e),o},t.timer=w,t.timerFlush=d,Object.defineProperty(t,"__esModule",{value:!0})});
