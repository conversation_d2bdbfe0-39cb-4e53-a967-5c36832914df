#!/usr/bin/env python3
"""
Test simple du système d'automatisation de scraping
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_automation_service():
    """Test du service d'automatisation"""
    logger.info("🤖 TEST DU SERVICE D'AUTOMATISATION")
    logger.info("=" * 50)
    
    try:
        from automation_service import AutomationService
        
        # Créer le service
        automation = AutomationService()
        logger.info("✅ Service d'automatisation créé")
        
        # Tester les statistiques
        stats = automation.get_stats()
        logger.info(f"📊 Statistiques actuelles:")
        logger.info(f"   - Total runs: {stats.get('total_runs', 0)}")
        logger.info(f"   - Successful runs: {stats.get('successful_runs', 0)}")
        logger.info(f"   - Failed runs: {stats.get('failed_runs', 0)}")
        logger.info(f"   - Files processed: {stats.get('files_processed', 0)}")
        logger.info(f"   - Last success: {stats.get('last_success', 'Jamais')}")
        logger.info(f"   - Last failure: {stats.get('last_failure', 'Jamais')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur: {e}")
        return False

def test_csv_import():
    """Test d'import CSV"""
    logger.info("\n📁 TEST D'IMPORT CSV")
    logger.info("=" * 50)
    
    try:
        from csv_importer import CSVImporter
        
        # Créer l'importeur
        importer = CSVImporter()
        logger.info("✅ Importeur CSV créé")
        
        # Vérifier la connexion DB
        connection = importer.get_connection()
        logger.info("✅ Connexion DB établie")
        
        # Vérifier les statistiques d'import
        stats = importer.get_import_stats()
        logger.info(f"📊 Statistiques d'import:")
        logger.info(f"   - Total records: {stats.get('total_records', 0)}")
        
        if 'top_countries' in stats:
            logger.info(f"   - Top 5 pays:")
            for country in stats['top_countries'][:5]:
                logger.info(f"     • {country['a_location_country']}: {country['count']} records")
        
        if 'verdict_stats' in stats:
            logger.info(f"   - Répartition des verdicts:")
            for verdict in stats['verdict_stats']:
                logger.info(f"     • {verdict['Verdict']}: {verdict['count']} records")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur: {e}")
        return False

def test_database_health():
    """Test de santé de la base de données"""
    logger.info("\n🗄️ TEST DE SANTÉ DE LA BASE DE DONNÉES")
    logger.info("=" * 50)
    
    try:
        from csv_importer import CSVImporter
        
        importer = CSVImporter()
        connection = importer.get_connection()
        
        with connection.cursor() as cursor:
            # Compter les enregistrements totaux
            cursor.execute("SELECT COUNT(*) as total FROM steeringofroaming")
            total = cursor.fetchone()['total']
            logger.info(f"📊 Total d'enregistrements: {total:,}")
            
            # Compter les enregistrements récents (dernières 24h)
            cursor.execute("""
                SELECT COUNT(*) as recent 
                FROM steeringofroaming 
                WHERE Timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            """)
            recent = cursor.fetchone()['recent']
            logger.info(f"🕐 Enregistrements dernières 24h: {recent:,}")
            
            # Compter les enregistrements par verdict
            cursor.execute("""
                SELECT Verdict, COUNT(*) as count 
                FROM steeringofroaming 
                GROUP BY Verdict 
                ORDER BY count DESC
            """)
            verdicts = cursor.fetchall()
            
            logger.info(f"📈 Répartition par verdict:")
            total_with_verdict = sum(v['count'] for v in verdicts)
            for verdict in verdicts:
                percentage = (verdict['count'] / total_with_verdict) * 100 if total_with_verdict > 0 else 0
                logger.info(f"   • {verdict['Verdict']}: {verdict['count']:,} ({percentage:.1f}%)")
            
            # Vérifier les pays les plus actifs
            cursor.execute("""
                SELECT a_location_country, COUNT(*) as count 
                FROM steeringofroaming 
                WHERE a_location_country IS NOT NULL 
                GROUP BY a_location_country 
                ORDER BY count DESC 
                LIMIT 5
            """)
            countries = cursor.fetchall()
            
            logger.info(f"🌍 Top 5 pays:")
            for country in countries:
                logger.info(f"   • {country['a_location_country']}: {country['count']:,} records")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur: {e}")
        return False

def test_scraping_simulation():
    """Simuler un cycle de scraping"""
    logger.info("\n🕷️ SIMULATION D'UN CYCLE DE SCRAPING")
    logger.info("=" * 50)
    
    try:
        from automation_service import AutomationService
        
        automation = AutomationService()
        
        # Simuler l'exécution (sans vraiment scraper)
        logger.info("🎯 Simulation d'un cycle de scraping et import...")
        
        # Vérifier s'il y a des fichiers de test
        test_data_dir = Path(__file__).parent / "test_data"
        csv_files = list(test_data_dir.glob("*.csv"))
        
        if csv_files:
            logger.info(f"📁 {len(csv_files)} fichier(s) CSV de test disponible(s)")
            
            # Simuler l'import d'un fichier de test
            test_file = csv_files[0]
            logger.info(f"📄 Test d'import avec: {test_file.name}")
            
            from csv_importer import CSVImporter
            importer = CSVImporter()
            
            # Compter les enregistrements avant
            connection = importer.get_connection()
            with connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) as count FROM steeringofroaming")
                count_before = cursor.fetchone()['count']
            connection.close()
            
            logger.info(f"📊 Enregistrements avant import: {count_before:,}")
            
            # Note: On ne fait pas l'import réel pour éviter les doublons
            logger.info("ℹ️ Import simulé (pas d'insertion réelle pour éviter les doublons)")
            
            logger.info("✅ Simulation de scraping réussie")
            return True
        else:
            logger.warning("⚠️ Aucun fichier de test disponible")
            return False
        
    except Exception as e:
        logger.error(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    logger.info("🚀 TEST SIMPLE DU SYSTÈME D'AUTOMATISATION")
    logger.info("=" * 60)
    logger.info(f"📅 Date/Heure: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("Service d'automatisation", test_automation_service),
        ("Import CSV", test_csv_import),
        ("Santé de la base de données", test_database_health),
        ("Simulation de scraping", test_scraping_simulation)
    ]
    
    results = {}
    passed = 0
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n🧪 EXÉCUTION: {test_name}")
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
                logger.info(f"✅ {test_name}: RÉUSSI")
            else:
                logger.error(f"❌ {test_name}: ÉCHOUÉ")
        except Exception as e:
            logger.error(f"💥 {test_name}: ERREUR - {e}")
            results[test_name] = False
    
    # Résumé final
    total = len(tests)
    success_rate = (passed / total) * 100
    
    logger.info(f"\n🎯 RÉSUMÉ FINAL")
    logger.info("=" * 60)
    logger.info(f"📊 Tests réussis: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        logger.info("🎉 SYSTÈME FONCTIONNEL!")
        logger.info("✅ Le scraping automatique est opérationnel")
    elif success_rate >= 60:
        logger.warning("⚠️ SYSTÈME PARTIELLEMENT FONCTIONNEL")
        logger.warning("🔧 Quelques corrections mineures nécessaires")
    else:
        logger.error("❌ SYSTÈME DÉFAILLANT")
        logger.error("🚨 Corrections urgentes requises")
    
    # Recommandations
    logger.info(f"\n💡 RECOMMANDATIONS:")
    if results.get("Service d'automatisation", False):
        logger.info("✅ Service d'automatisation opérationnel")
    else:
        logger.info("🔧 Vérifier la configuration du service d'automatisation")
    
    if results.get("Santé de la base de données", False):
        logger.info("✅ Base de données en bonne santé")
    else:
        logger.info("🔧 Vérifier la connexion et la structure de la base de données")
    
    logger.info(f"\n📋 PROCHAINES ÉTAPES:")
    logger.info("1. Configurer les identifiants GlobalRoamer dans .env")
    logger.info("2. Tester la connectivité vers GlobalRoamer")
    logger.info("3. Programmer l'exécution quotidienne à 2h du matin")
    logger.info("4. Surveiller les logs d'automatisation")
    
    return success_rate >= 60

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
