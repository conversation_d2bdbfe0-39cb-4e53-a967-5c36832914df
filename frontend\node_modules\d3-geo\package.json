{"_from": "d3-geo@^2.0.2", "_id": "d3-geo@2.0.2", "_inBundle": false, "_integrity": "sha512-8pM1WGMLGFuhq9S+FpPURxic+gKzjluCD/CHTuUF3mXMeiCo0i6R0tO1s4+GArRFde96SLcW/kOFRjoAosPsFA==", "_location": "/d3-geo", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-geo@^2.0.2", "name": "d3-geo", "escapedName": "d3-geo", "rawSpec": "^2.0.2", "saveSpec": null, "fetchSpec": "^2.0.2"}, "_requiredBy": ["/react-simple-maps"], "_resolved": "https://registry.npmjs.org/d3-geo/-/d3-geo-2.0.2.tgz", "_shasum": "c065c1b71fe8c5f1be657e5f43d9bdd010383c40", "_spec": "d3-geo@^2.0.2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\react-simple-maps", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-geo/issues"}, "bundleDependencies": false, "dependencies": {"d3-array": "^2.5.0"}, "deprecated": false, "description": "Shapes and calculators for spherical coordinates.", "devDependencies": {"canvas": "1 - 2", "d3-format": "1 - 2", "eslint": "6", "esm": "3", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4", "topojson-client": "3", "world-atlas": "1"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-geo/", "jsdelivr": "dist/d3-geo.min.js", "keywords": ["d3", "d3-module", "geo", "maps", "cartography"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-geo.js", "module": "src/index.js", "name": "d3-geo", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-geo.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test && mkdir -p test/output && test/compare-images", "pretest": "rollup -c", "test": "tape -r esm 'test/**/*-test.js' && eslint src"}, "sideEffects": false, "unpkg": "dist/d3-geo.min.js", "version": "2.0.2"}