{"_from": "@types/d3-zoom@^2", "_id": "@types/d3-zoom@2.0.7", "_inBundle": false, "_integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>E8ZyrKUQ68ESTWSK16fVb0OYnaiJ+WXJRYxKLn4aXU0o4CLYxMWBEiouUfO3TTCoyroOrGPcBG6u1aAxA==", "_location": "/@types/d3-zoom", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/d3-zoom@^2", "name": "@types/d3-zoom", "escapedName": "@types%2fd3-zoom", "scope": "@types", "rawSpec": "^2", "saveSpec": null, "fetchSpec": "^2"}, "_requiredBy": ["/@types/react-simple-maps"], "_resolved": "https://registry.npmjs.org/@types/d3-zoom/-/d3-zoom-2.0.7.tgz", "_shasum": "bd0c7376c53f90be28340507f519bc91a4736120", "_spec": "@types/d3-zoom@^2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\@types\\react-simple-maps", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "denis<PERSON>", "url": "https://github.com/denisname"}, {"name": "<PERSON>", "url": "https://github.com/Methuselah96"}], "dependencies": {"@types/d3-interpolate": "^2", "@types/d3-selection": "^2"}, "deprecated": false, "description": "TypeScript definitions for d3-zoom", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-zoom", "license": "MIT", "main": "", "name": "@types/d3-zoom", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-zoom"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "3a0c09b2c9ae9c41399c7556604e2289192827183e55ec610cc5d7ed40a2b7ed", "version": "2.0.7"}