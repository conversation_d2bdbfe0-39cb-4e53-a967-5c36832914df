{"_from": "@react-leaflet/core@^3.0.0", "_id": "@react-leaflet/core@3.0.0", "_inBundle": false, "_integrity": "sha512-3EWmekh4Nz+pGcr+xjf0KNyYfC3U2JjnkWsh0zcqaexYqmmB5ZhH37kz41JXGmKzpaMZCnPofBBm64i+YrEvGQ==", "_location": "/@react-leaflet/core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@react-leaflet/core@^3.0.0", "name": "@react-leaflet/core", "escapedName": "@react-leaflet%2fcore", "scope": "@react-leaflet", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/react-leaflet"], "_resolved": "https://registry.npmjs.org/@react-leaflet/core/-/core-3.0.0.tgz", "_shasum": "34ccc280ce7d8ac5c09f2b3d5fffded450bdf1a2", "_spec": "@react-leaflet/core@^3.0.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\react-leaflet", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/PaulLeCam/react-leaflet/issues"}, "bundleDependencies": false, "deprecated": false, "description": "React Leaflet core", "devDependencies": {"@types/react": "^19.0.1", "@types/react-dom": "^19.0.2"}, "exports": {".": "./lib/index.js"}, "files": ["lib/*"], "homepage": "https://react-leaflet.js.org/docs/core-introduction", "jest": {"extensionsToTreatAsEsm": [".ts", ".tsx"], "resolver": "ts-jest-resolver", "testEnvironment": "jsdom", "transform": {"^.+\\.(t|j)sx?$": ["@swc/jest", {"root": "../.."}]}}, "keywords": ["react-component", "react", "leaflet", "map"], "license": "Hippocratic-2.1", "main": "lib/index.js", "name": "@react-leaflet/core", "peerDependencies": {"leaflet": "^1.9.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/PaulLeCam/react-leaflet.git"}, "scripts": {"build": "pnpm run build:clean && pnpm run build:types && pnpm run build:js", "build:clean": "del lib", "build:js": "swc src -d ./lib --config-file  ../../.swcrc --strip-leading-paths", "build:types": "tsc --emitDeclarationOnly", "start": "pnpm run test && pnpm run build", "test": "pnpm run test:types && pnpm run test:unit", "test:types": "tsc --noEmit", "test:unit": "cross-env NODE_ENV=test jest"}, "sideEffects": false, "type": "module", "types": "lib/index.d.ts", "version": "3.0.0"}