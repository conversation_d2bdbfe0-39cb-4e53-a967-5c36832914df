{"_from": "@types/d3-geo@^2", "_id": "@types/d3-geo@2.0.7", "_inBundle": false, "_integrity": "sha512-RIXlxPdxvX+LAZFv+t78CuYpxYag4zuw9mZc+AwfB8tZpKU90rMEn2il2ADncmeZlb7nER9dDsJpRisA3lRvjA==", "_location": "/@types/d3-geo", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/d3-geo@^2", "name": "@types/d3-geo", "escapedName": "@types%2fd3-geo", "scope": "@types", "rawSpec": "^2", "saveSpec": null, "fetchSpec": "^2"}, "_requiredBy": ["/@types/react-simple-maps"], "_resolved": "https://registry.npmjs.org/@types/d3-geo/-/d3-geo-2.0.7.tgz", "_shasum": "7ad48988e9af14e1e8490e25e926e5050992b397", "_spec": "@types/d3-geo@^2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\@types\\react-simple-maps", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/ledragon"}, {"name": "<PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "url": "https://github.com/Methuselah96"}], "dependencies": {"@types/geojson": "*"}, "deprecated": false, "description": "TypeScript definitions for d3-geo", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-geo", "license": "MIT", "main": "", "name": "@types/d3-geo", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-geo"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "b1472fc87ec89e12659e34da7f8000ed852d84497f4854f0a06c877bd00bdb8b", "version": "2.0.7"}