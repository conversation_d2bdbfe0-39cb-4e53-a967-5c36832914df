import RefreshIcon from '@mui/icons-material/Refresh';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, CardContent, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>Chart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { getSteeringSuccessByCountry } from '../services/kpiService';

interface SteeringData {
  country: string;
  steering_success_rate: number;
  total_requests: number;
  failures: number;
}

interface SteeringAnalysisChartProps {
  title?: string;
  height?: string;
  startDate?: string;
  endDate?: string;
}

const SteeringAnalysisChart: React.FC<SteeringAnalysisChartProps> = ({ 
  title = "Analyse du Steering par Pays", 
  height = "400px",
  startDate,
  endDate 
}) => {
  const [data, setData] = useState<SteeringData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const filters = {
        start_date: startDate,
        end_date: endDate
      };
      
      console.log("Fetching data with filters:", filters);
      const result = await getSteeringSuccessByCountry(filters);
      
      if (!result.success) {
        throw new Error(result.error || 'Erreur lors de la récupération des données');
      }
      
      console.log("Received data:", result.data);
      setData(result.data);
    } catch (err) {
      console.error("Error fetching data:", err);
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [startDate, endDate]);

  // Préparer les données pour le graphique
  const chartData = data.map(item => ({
    country: item.country,
    successful: Math.round(item.total_requests * (item.steering_success_rate / 100)),
    failed: item.failures,
    total: item.total_requests,
    successRate: item.steering_success_rate
  })).sort((a, b) => b.total - a.total);

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="div">
            {title}
          </Typography>
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={fetchData}
            disabled={loading}
          >
            Rafraîchir
          </Button>
        </Box>

        {error && (
          <Typography color="error" gutterBottom>
            {error}
          </Typography>
        )}

        {loading ? (
          <Typography>Chargement des données...</Typography>
        ) : data.length === 0 ? (
          <Typography>Aucune donnée disponible pour la période sélectionnée</Typography>
        ) : (
          <Box sx={{ width: '100%', height }}>
            <ResponsiveContainer>
              <BarChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="country" 
                  angle={-45}
                  textAnchor="end"
                  height={100}
                  interval={0}
                />
                <YAxis />
                <Tooltip 
                  formatter={(value: number, name: string) => {
                    if (name === "Tentatives réussies" || name === "Tentatives échouées") {
                      return [value, 'Tentatives'];
                    }
                    return [value, name];
                  }}
                  labelFormatter={(label) => `Pays: ${label}`}
                />
                <Legend />
                <Bar dataKey="successful" name="Tentatives réussies" stackId="a" fill="#4caf50" />
                <Bar dataKey="failed" name="Tentatives échouées" stackId="a" fill="#f44336" />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default SteeringAnalysisChart; 