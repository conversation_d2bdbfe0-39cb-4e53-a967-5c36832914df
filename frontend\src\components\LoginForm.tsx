import { FormEvent, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { firebaseAuthService } from '../services/firebaseAuth';

const LoginForm = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isRegistering, setIsRegistering] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { login, status } = useAuth();
  const navigate = useNavigate();
  
  // Rediriger si déjà authentifié
  useEffect(() => {
    if (status === 'authenticated') {
      navigate('/dashboard');
    }
  }, [status, navigate]);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      setError('Email et mot de passe requis');
      toast.error('Email et mot de passe requis');
      return;
    }
    
    setLoading(true);
    setError(null);

    try {
      if (isRegistering) {
        // Création d'un compte avec Firebase
        await firebaseAuthService.register(email, password);
        toast.success('Compte créé avec succès!');
      }
      
      // Utiliser le contexte d'authentification pour la session
      await login(email, password);
      
      toast.success('Connexion réussie!');
      
      // Utiliser React Router pour la navigation sans rechargement
      navigate('/dashboard', { replace: true });
    } catch (error: any) {
      console.error('Erreur d\'authentification:', error);
      
      // Messages d'erreur personnalisés selon le code d'erreur Firebase
      let errorMessage = 'Erreur d\'authentification. Vérifiez vos identifiants ou la connexion réseau.';
      
      if (error.code) {
        switch (error.code) {
          case 'auth/user-not-found':
            errorMessage = 'Aucun utilisateur trouvé avec cet email.';
            break;
          case 'auth/wrong-password':
            errorMessage = 'Mot de passe incorrect.';
            break;
          case 'auth/email-already-in-use':
            errorMessage = 'Cet email est déjà utilé par un autre compte.';
            break;
          case 'auth/weak-password':
            errorMessage = 'Le mot de passe est trop faible. Utilisez au moins 6 caractères.';
            break;
          case 'auth/invalid-email':
            errorMessage = 'Format d\'email invalide.';
            break;
        }
      }
      
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <h2 className="text-2xl font-bold mb-6 text-center">
        {isRegistering ? 'Créer un compte' : 'Connexion'}
      </h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            id="email"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
        
        <div className="mb-6">
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
            Mot de passe
          </label>
          <input
            id="password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            required
            minLength={6}
          />
          {isRegistering && (
            <p className="text-xs text-gray-500 mt-1">
              Le mot de passe doit contenir au moins 6 caractères
            </p>
          )}
        </div>
        
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          disabled={loading}
        >
          {loading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin h-5 w-5 mr-3" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
              Chargement...
            </span>
          ) : (
            isRegistering ? 'Créer un compte' : 'Se connecter'
          )}
        </button>
      </form>
      
      <div className="mt-4 text-center">
        <button
          onClick={() => setIsRegistering(!isRegistering)}
          className="text-blue-600 hover:underline"
        >
          {isRegistering ? 'Déjà un compte? Se connecter' : 'Créer un compte'}
        </button>
      </div>
    </div>
  );
};

export default LoginForm; 
 
 