{"_from": "d3-dispatch@1 - 2", "_id": "d3-dispatch@2.0.0", "_inBundle": false, "_integrity": "sha512-S/m2VsXI7gAti2pBoLClFFTMOO1HTtT0j99AuXLoGFKO6deHDdnv6ZGTxSTTUTgO1zVcv82fCOtDjYK4EECmWA==", "_location": "/d3-dispatch", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-dispatch@1 - 2", "name": "d3-dispatch", "escapedName": "d3-dispatch", "rawSpec": "1 - 2", "saveSpec": null, "fetchSpec": "1 - 2"}, "_requiredBy": ["/d3-drag", "/d3-transition", "/d3-zoom"], "_resolved": "https://registry.npmjs.org/d3-dispatch/-/d3-dispatch-2.0.0.tgz", "_shasum": "8a18e16f76dd3fcaef42163c97b926aa9b55e7cf", "_spec": "d3-dispatch@1 - 2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\d3-zoom", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-dispatch/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Register named callbacks and call them with arguments.", "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-dispatch/", "jsdelivr": "dist/d3-dispatch.min.js", "keywords": ["d3", "d3-module", "event", "listener", "dispatch"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-dispatch.js", "module": "src/index.js", "name": "d3-dispatch", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-dispatch.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src test"}, "sideEffects": false, "unpkg": "dist/d3-dispatch.min.js", "version": "2.0.0"}