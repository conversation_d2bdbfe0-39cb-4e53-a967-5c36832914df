{"_from": "commander@2", "_id": "commander@2.20.3", "_inBundle": false, "_integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "_location": "/topoj<PERSON>-client/commander", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "commander@2", "name": "commander", "escapedName": "commander", "rawSpec": "2", "saveSpec": null, "fetchSpec": "2"}, "_requiredBy": ["/topojson-client"], "_resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "_shasum": "fd485e84c03eb4881c20722ba48035e8531aeb33", "_spec": "commander@2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\topojson-client", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/tj/commander.js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "the complete solution for node.js command-line programs", "devDependencies": {"@types/node": "^12.7.8", "eslint": "^6.4.0", "should": "^13.2.3", "sinon": "^7.5.0", "standard": "^14.3.1", "ts-node": "^8.4.1", "typescript": "^3.6.3"}, "files": ["index.js", "typings/index.d.ts"], "homepage": "https://github.com/tj/commander.js#readme", "keywords": ["commander", "command", "option", "parser"], "license": "MIT", "main": "index", "name": "commander", "repository": {"type": "git", "url": "git+https://github.com/tj/commander.js.git"}, "scripts": {"lint": "eslint index.js", "test": "node test/run.js && npm run test-typings", "test-typings": "tsc -p tsconfig.json"}, "typings": "typings/index.d.ts", "version": "2.20.3"}