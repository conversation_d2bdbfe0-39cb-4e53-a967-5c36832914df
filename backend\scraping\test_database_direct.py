#!/usr/bin/env python3
"""
Test direct de la base de données pour vérifier l'import des colonnes
"""

import pymysql
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_connection():
    """Test direct de la connexion et des données"""
    logger.info("🔍 TEST DIRECT DE LA BASE DE DONNÉES")
    logger.info("=" * 50)
    
    try:
        # Configuration de connexion
        config = {
            'host': 'localhost',
            'user': 'root',
            'password': '',
            'database': 'kpi',
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        
        # Connexion
        connection = pymysql.connect(**config)
        logger.info("✅ Connexion à la base de données réussie")
        
        with connection.cursor() as cursor:
            # Vérifier les tables
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            logger.info(f"📋 Tables trouvées:")
            for table in tables:
                table_name = list(table.values())[0]
                logger.info(f"   • {table_name}")
            
            # Vérifier la table steeringofroaming
            if any('steeringofroaming' in str(table.values()) for table in tables):
                logger.info(f"\n📊 ANALYSE DE LA TABLE steeringofroaming:")
                
                # Compter les enregistrements
                cursor.execute("SELECT COUNT(*) as total FROM steeringofroaming")
                total = cursor.fetchone()['total']
                logger.info(f"   📈 Total d'enregistrements: {total:,}")
                
                # Vérifier les colonnes
                cursor.execute("SHOW COLUMNS FROM steeringofroaming")
                columns = cursor.fetchall()
                
                logger.info(f"   📋 {len(columns)} colonnes:")
                for i, col in enumerate(columns[:20], 1):  # Afficher les 20 premières
                    logger.info(f"      {i:2d}. {col['Field']}")
                
                if len(columns) > 20:
                    logger.info(f"      ... et {len(columns) - 20} autres colonnes")
                
                # Vérifier un échantillon de données
                cursor.execute("SELECT * FROM steeringofroaming LIMIT 1")
                sample = cursor.fetchone()
                
                if sample:
                    logger.info(f"\n   📄 ÉCHANTILLON DE DONNÉES:")
                    non_null_fields = 0
                    for field, value in sample.items():
                        if value is not None and value != '':
                            non_null_fields += 1
                            if non_null_fields <= 10:  # Afficher les 10 premiers champs non-null
                                logger.info(f"      {field}: {str(value)[:50]}{'...' if len(str(value)) > 50 else ''}")
                    
                    logger.info(f"   ✅ {non_null_fields} champs avec des données sur {len(sample)} colonnes")
                
                # Vérifier les pays
                cursor.execute("""
                    SELECT a_location_country, COUNT(*) as count 
                    FROM steeringofroaming 
                    WHERE a_location_country IS NOT NULL AND a_location_country != ''
                    GROUP BY a_location_country 
                    ORDER BY count DESC 
                    LIMIT 5
                """)
                countries = cursor.fetchall()
                
                if countries:
                    logger.info(f"\n   🌍 TOP 5 PAYS:")
                    for country in countries:
                        logger.info(f"      • {country['a_location_country']}: {country['count']} records")
                else:
                    logger.warning("   ⚠️ Aucun pays trouvé dans a_location_country")
                
                # Vérifier les verdicts
                cursor.execute("""
                    SELECT Verdict, COUNT(*) as count 
                    FROM steeringofroaming 
                    WHERE Verdict IS NOT NULL AND Verdict != ''
                    GROUP BY Verdict 
                    ORDER BY count DESC
                """)
                verdicts = cursor.fetchall()
                
                if verdicts:
                    logger.info(f"\n   📊 VERDICTS:")
                    for verdict in verdicts:
                        logger.info(f"      • {verdict['Verdict']}: {verdict['count']} records")
                else:
                    logger.warning("   ⚠️ Aucun verdict trouvé")
            
            else:
                logger.error("❌ Table steeringofroaming non trouvée")
        
        connection.close()
        logger.info(f"\n✅ Test terminé avec succès")
        return True
        
    except Exception as e:
        logger.error(f"❌ Erreur lors du test: {e}")
        return False

def main():
    """Fonction principale"""
    logger.info("🚀 TEST DIRECT DE LA BASE DE DONNÉES")
    logger.info("=" * 60)
    
    success = test_database_connection()
    
    if success:
        logger.info("🎉 BASE DE DONNÉES ACCESSIBLE ET FONCTIONNELLE!")
    else:
        logger.error("❌ PROBLÈME AVEC LA BASE DE DONNÉES")
    
    return success

if __name__ == "__main__":
    main()
