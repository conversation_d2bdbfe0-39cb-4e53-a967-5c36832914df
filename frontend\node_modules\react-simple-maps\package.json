{"_from": "react-simple-maps", "_id": "react-simple-maps@3.0.0", "_inBundle": false, "_integrity": "sha512-vKNFrvpPG8Vyfdjnz5Ne1N56rZlDfHXv5THNXOVZMqbX1rWZA48zQuYT03mx6PAKanqarJu/PDLgshIZAfHHqw==", "_location": "/react-simple-maps", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "react-simple-maps", "name": "react-simple-maps", "escapedName": "react-simple-maps", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/react-simple-maps/-/react-simple-maps-3.0.0.tgz", "_shasum": "2349d884c9ba37b68695b9f5e1e7d9c2a826c00e", "_spec": "react-simple-maps", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/zimrick"}, "browser": "dist/index.umd.js", "bugs": {"url": "https://github.com/zcreativelabs/react-simple-maps/issues"}, "bundleDependencies": false, "dependencies": {"d3-geo": "^2.0.2", "d3-selection": "^2.0.0", "d3-zoom": "^2.0.0", "topojson-client": "^3.1.0"}, "deprecated": false, "description": "An svg map chart component built with and for React", "devDependencies": {"@babel/core": "^7.18.6", "@babel/plugin-external-helpers": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.18.6", "@babel/plugin-transform-react-jsx": "^7.18.6", "@babel/preset-env": "^7.18.6", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.1", "@rollup/plugin-node-resolve": "^13.3.0", "expect": "^23.5.0", "mocha": "^5.2.0", "prop-types": "^15.7.2", "react": "^17.0.1", "react-dom": "^17.0.1", "react-test-utils": "^0.0.1", "rollup": "^2.75.7", "rollup-plugin-terser": "^7.0.2"}, "files": ["dist"], "homepage": "https://github.com/zcreativelabs/react-simple-maps#readme", "keywords": ["react", "maps", "charts", "worldmap", "usa", "d3-geo"], "license": "MIT", "main": "dist/index.js", "module": "dist/index.es.js", "name": "react-simple-maps", "peerDependencies": {"prop-types": "^15.7.2", "react": "^16.8.0 || 17.x || 18.x", "react-dom": "^16.8.0 || 17.x || 18.x"}, "repository": {"type": "git", "url": "git+https://github.com/zcreativelabs/react-simple-maps.git"}, "scripts": {"build": "rollup -c", "prepare": "rollup -c", "test": "mocha './tests/**/*.spec.js' --compilers js:babel-core/register", "watch": "rollup -cw"}, "version": "3.0.0"}