#!/usr/bin/env python3
"""
Script pour télécharger et configurer ChromeDriver manuellement
"""

import os
import requests
import zipfile
import json
import subprocess
import sys
from pathlib import Path

def get_chrome_version():
    """Obtenir la version de Chrome installée"""
    try:
        # Windows
        if sys.platform.startswith('win'):
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', ''))
            ]
            
            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    # Utiliser PowerShell pour obtenir la version
                    cmd = f'(Get-Item "{chrome_path}").VersionInfo.ProductVersion'
                    result = subprocess.run(['powershell', '-Command', cmd], 
                                          capture_output=True, text=True)
                    if result.returncode == 0:
                        version = result.stdout.strip()
                        print(f"Chrome version trouvée: {version}")
                        return version.split('.')[0]  # Retourner seulement le numéro majeur
        
        print("Chrome non trouvé ou version non détectable")
        return "120"  # Version par défaut
        
    except Exception as e:
        print(f"Erreur lors de la détection de Chrome: {e}")
        return "120"

def download_chromedriver(version="120"):
    """Télécharger ChromeDriver pour la version spécifiée"""
    try:
        print(f"Téléchargement de ChromeDriver version {version}...")
        
        # URL pour ChromeDriver
        base_url = "https://chromedriver.storage.googleapis.com"
        
        # Obtenir la liste des versions disponibles
        try:
            # Essayer l'ancienne API d'abord
            releases_url = f"{base_url}/LATEST_RELEASE_{version}"
            response = requests.get(releases_url, timeout=30, verify=False)
            
            if response.status_code == 200:
                latest_version = response.text.strip()
                print(f"Version ChromeDriver trouvée: {latest_version}")
            else:
                # Fallback vers une version connue
                latest_version = f"{version}.0.6099.109"
                print(f"Utilisation de la version par défaut: {latest_version}")
                
        except Exception as e:
            print(f"Erreur lors de la récupération de la version: {e}")
            latest_version = f"{version}.0.6099.109"
        
        # URL de téléchargement
        if sys.platform.startswith('win'):
            platform = "win32"
        else:
            platform = "linux64"
            
        download_url = f"{base_url}/{latest_version}/chromedriver_{platform}.zip"
        print(f"URL de téléchargement: {download_url}")
        
        # Créer le répertoire de destination
        driver_dir = Path(__file__).parent / "drivers"
        driver_dir.mkdir(exist_ok=True)
        
        # Télécharger le fichier
        print("Téléchargement en cours...")
        response = requests.get(download_url, timeout=60, verify=False)
        
        if response.status_code == 200:
            zip_path = driver_dir / "chromedriver.zip"
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            print("Extraction du fichier...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(driver_dir)
            
            # Supprimer le fichier zip
            zip_path.unlink()
            
            # Rendre exécutable sur Linux/Mac
            if not sys.platform.startswith('win'):
                chromedriver_path = driver_dir / "chromedriver"
                os.chmod(chromedriver_path, 0o755)
            else:
                chromedriver_path = driver_dir / "chromedriver.exe"
            
            print(f"✅ ChromeDriver installé avec succès: {chromedriver_path}")
            return str(chromedriver_path)
            
        else:
            print(f"❌ Erreur de téléchargement: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lors du téléchargement: {e}")
        return None

def test_chromedriver(driver_path):
    """Tester ChromeDriver"""
    try:
        print(f"Test de ChromeDriver: {driver_path}")
        
        if not os.path.exists(driver_path):
            print("❌ ChromeDriver non trouvé")
            return False
        
        # Test simple
        result = subprocess.run([driver_path, '--version'], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ ChromeDriver fonctionne: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ Erreur ChromeDriver: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 CONFIGURATION DE CHROMEDRIVER")
    print("=" * 40)
    
    # Détecter la version de Chrome
    chrome_version = get_chrome_version()
    print(f"Version de Chrome détectée: {chrome_version}")
    
    # Télécharger ChromeDriver
    driver_path = download_chromedriver(chrome_version)
    
    if driver_path:
        # Tester ChromeDriver
        if test_chromedriver(driver_path):
            print("\n🎉 Configuration réussie!")
            print(f"ChromeDriver installé dans: {driver_path}")
            
            # Créer un fichier de configuration
            config = {
                "chromedriver_path": driver_path,
                "chrome_version": chrome_version
            }
            
            config_path = Path(__file__).parent / "chromedriver_config.json"
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            print(f"Configuration sauvegardée dans: {config_path}")
            return True
        else:
            print("\n❌ Échec du test de ChromeDriver")
            return False
    else:
        print("\n❌ Échec du téléchargement de ChromeDriver")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 Solutions alternatives:")
        print("1. Téléchargez ChromeDriver manuellement depuis https://chromedriver.chromium.org/")
        print("2. Placez chromedriver.exe dans le dossier 'drivers'")
        print("3. Ou ajoutez ChromeDriver à votre PATH système")
