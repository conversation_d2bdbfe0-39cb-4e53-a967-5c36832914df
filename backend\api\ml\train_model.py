#!/usr/bin/env python3
"""
Script d'entraînement automatique du modèle ML pour la détection d'anomalies KPI
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import argparse
import json

# Ajouter le répertoire parent au path pour les imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from kpi_predictor import KPIAnomalyDetector
from ..get_db_connection import get_db_connection
from ..services.ml_service import MLService

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ModelTrainer:
    def __init__(self):
        self.ml_service = MLService()
        self.model = KPIAnomalyDetector()
        
    def prepare_training_data(self, days_back: int = 90) -> pd.DataFrame:
        """
        Prépare les données d'entraînement depuis la base de données
        
        Args:
            days_back: Nombre de jours de données historiques à récupérer
            
        Returns:
            DataFrame avec les données préparées pour l'entraînement
        """
        logger.info(f"📊 Récupération de {days_back} jours de données historiques...")
        
        try:
            # Récupérer les données depuis la base
            df = self.ml_service.get_training_data(days_back)
            
            if df.empty:
                raise ValueError("Aucune donnée trouvée dans la base de données")
            
            logger.info(f"✅ {len(df)} enregistrements récupérés")
            
            # Calculer des KPIs supplémentaires pour l'entraînement
            df = self._calculate_additional_kpis(df)
            
            # Nettoyer les données
            df = self._clean_data(df)
            
            logger.info(f"🧹 Données nettoyées: {len(df)} enregistrements finaux")
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la préparation des données: {e}")
            raise
    
    def _calculate_additional_kpis(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calcule des KPIs supplémentaires pour améliorer la détection d'anomalies"""
        logger.info("🔢 Calcul des KPIs supplémentaires...")
        
        # Taux de succès par heure
        df['hourly_success_rate'] = df['Success'].rolling('1H').mean()
        
        # Durée moyenne d'attachement par heure
        df['hourly_avg_duration'] = df['lup_duration_seconds'].rolling('1H').mean()
        
        # Nombre de rejets par heure
        df['hourly_rejections'] = df['a_NrOfPlmnsRejected'].rolling('1H').sum()
        
        # Taux de changement (dérivée)
        df['success_rate_change'] = df['success_rate'].diff()
        df['duration_change'] = df['avg_lup_duration'].diff()
        
        # Volatilité (écart-type mobile)
        df['success_rate_volatility'] = df['success_rate'].rolling('6H').std()
        df['duration_volatility'] = df['avg_lup_duration'].rolling('6H').std()
        
        # Indicateurs de performance par pays
        country_stats = df.groupby('a_location_country').agg({
            'Success': 'mean',
            'lup_duration_seconds': 'mean'
        }).add_suffix('_country_avg')
        
        df = df.join(country_stats, on='a_location_country')
        
        # Écart par rapport à la moyenne du pays
        df['success_vs_country'] = df['Success'] - df['Success_country_avg']
        df['duration_vs_country'] = df['lup_duration_seconds'] - df['lup_duration_seconds_country_avg']
        
        return df
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Nettoie les données pour l'entraînement"""
        logger.info("🧹 Nettoyage des données...")
        
        # Supprimer les colonnes non numériques pour l'entraînement
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        df_clean = df[numeric_columns].copy()
        
        # Remplacer les valeurs infinies par NaN
        df_clean = df_clean.replace([np.inf, -np.inf], np.nan)
        
        # Supprimer les lignes avec trop de valeurs manquantes (>50%)
        threshold = len(df_clean.columns) * 0.5
        df_clean = df_clean.dropna(thresh=threshold)
        
        # Remplir les valeurs manquantes restantes
        for col in df_clean.columns:
            if df_clean[col].isna().any():
                # Utiliser la médiane pour les colonnes numériques
                df_clean[col] = df_clean[col].fillna(df_clean[col].median())
        
        return df_clean
    
    def train_and_evaluate(self, df: pd.DataFrame, test_size: float = 0.2) -> dict:
        """
        Entraîne le modèle et évalue ses performances
        
        Args:
            df: DataFrame avec les données d'entraînement
            test_size: Proportion des données pour le test
            
        Returns:
            Dictionnaire avec les résultats d'entraînement et d'évaluation
        """
        logger.info("🚀 Début de l'entraînement du modèle...")
        
        try:
            # Diviser les données en train/test
            split_index = int(len(df) * (1 - test_size))
            train_data = df.iloc[:split_index]
            test_data = df.iloc[split_index:]
            
            logger.info(f"📊 Données d'entraînement: {len(train_data)} enregistrements")
            logger.info(f"📊 Données de test: {len(test_data)} enregistrements")
            
            # Entraîner le modèle
            start_time = datetime.now()
            self.model.train(train_data)
            training_time = (datetime.now() - start_time).total_seconds()
            
            logger.info(f"✅ Entraînement terminé en {training_time:.2f} secondes")
            
            # Évaluer sur les données de test
            test_results = self.model.predict_anomalies(test_data)
            
            # Calculer des métriques d'évaluation
            evaluation_metrics = self._calculate_evaluation_metrics(test_data, test_results)
            
            # Sauvegarder les résultats
            training_summary = {
                "timestamp": datetime.now().isoformat(),
                "training_data_size": len(train_data),
                "test_data_size": len(test_data),
                "training_time_seconds": training_time,
                "features_count": len(train_data.columns),
                "anomalies_detected": len(test_results["anomalies"]),
                "evaluation_metrics": evaluation_metrics,
                "model_parameters": {
                    "contamination": self.model.isolation_forest.contamination,
                    "n_estimators": self.model.predictor.n_estimators,
                    "z_score_threshold": self.model.z_score_threshold,
                    "ma_window": self.model.ma_window
                }
            }
            
            # Sauvegarder le résumé
            self._save_training_summary(training_summary)
            
            logger.info("🎉 Entraînement et évaluation terminés avec succès!")
            
            return training_summary
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'entraînement: {e}")
            raise
    
    def _calculate_evaluation_metrics(self, test_data: pd.DataFrame, results: dict) -> dict:
        """Calcule les métriques d'évaluation du modèle"""
        logger.info("📈 Calcul des métriques d'évaluation...")
        
        anomalies = results["anomalies"]
        total_points = len(test_data)
        anomaly_rate = len(anomalies) / total_points if total_points > 0 else 0
        
        # Analyser la distribution des anomalies par méthode
        methods_summary = results["methods_summary"]
        
        # Calculer la cohérence entre les méthodes
        consensus_rate = results["consensus_count"] / len(anomalies) if anomalies else 0
        
        return {
            "total_test_points": total_points,
            "anomalies_detected": len(anomalies),
            "anomaly_rate_percent": round(anomaly_rate * 100, 2),
            "consensus_rate_percent": round(consensus_rate * 100, 2),
            "methods_summary": methods_summary,
            "severity_distribution": {
                "high": sum(1 for a in anomalies if a["severity"] == "high"),
                "medium": sum(1 for a in anomalies if a["severity"] == "medium")
            }
        }
    
    def _save_training_summary(self, summary: dict):
        """Sauvegarde le résumé d'entraînement"""
        try:
            # Créer le dossier de logs s'il n'existe pas
            logs_dir = os.path.join(self.model.model_path, "training_logs")
            os.makedirs(logs_dir, exist_ok=True)
            
            # Nom du fichier avec timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"training_summary_{timestamp}.json"
            filepath = os.path.join(logs_dir, filename)
            
            # Sauvegarder
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📄 Résumé sauvegardé: {filepath}")
            
        except Exception as e:
            logger.warning(f"⚠️ Impossible de sauvegarder le résumé: {e}")

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Entraînement du modèle ML pour la détection d'anomalies KPI")
    parser.add_argument("--days", type=int, default=90, help="Nombre de jours de données historiques (défaut: 90)")
    parser.add_argument("--test-size", type=float, default=0.2, help="Proportion des données pour le test (défaut: 0.2)")
    parser.add_argument("--force", action="store_true", help="Forcer l'entraînement même si un modèle existe")
    
    args = parser.parse_args()
    
    logger.info("🤖 DÉBUT DE L'ENTRAÎNEMENT DU MODÈLE ML")
    logger.info("=" * 50)
    
    try:
        trainer = ModelTrainer()
        
        # Vérifier si un modèle existe déjà
        if not args.force and trainer.model.load_models():
            logger.info("⚠️ Un modèle existe déjà. Utilisez --force pour le remplacer.")
            return
        
        # Préparer les données
        df = trainer.prepare_training_data(args.days)
        
        # Entraîner et évaluer
        results = trainer.train_and_evaluate(df, args.test_size)
        
        # Afficher les résultats
        print("\n🎉 RÉSULTATS D'ENTRAÎNEMENT:")
        print(f"📊 Données d'entraînement: {results['training_data_size']} enregistrements")
        print(f"📊 Données de test: {results['test_data_size']} enregistrements")
        print(f"⏱️ Temps d'entraînement: {results['training_time_seconds']:.2f} secondes")
        print(f"🔍 Anomalies détectées: {results['anomalies_detected']}")
        print(f"📈 Taux d'anomalies: {results['evaluation_metrics']['anomaly_rate_percent']}%")
        
        logger.info("✅ Entraînement terminé avec succès!")
        
    except Exception as e:
        logger.error(f"💥 Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
