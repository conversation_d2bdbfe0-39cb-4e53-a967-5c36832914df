{"_from": "@types/d3-color@^2", "_id": "@types/d3-color@2.0.6", "_inBundle": false, "_integrity": "sha512-tbaFGDmJWHqnenvk3QGSvD3RVwr631BjKRD7Sc7VLRgrdX5mk5hTyoeBL6rXZaeoXzmZwIl1D2HPogEdt1rHBg==", "_location": "/@types/d3-zoom/@types/d3-color", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/d3-color@^2", "name": "@types/d3-color", "escapedName": "@types%2fd3-color", "scope": "@types", "rawSpec": "^2", "saveSpec": null, "fetchSpec": "^2"}, "_requiredBy": ["/@types/d3-zoom/@types/d3-interpolate"], "_resolved": "https://registry.npmjs.org/@types/d3-color/-/d3-color-2.0.6.tgz", "_shasum": "88a9a06afea2d3a400ea650a6c3e60e9bf9b0f2a", "_spec": "@types/d3-color@^2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\@types\\d3-zoom\\node_modules\\@types\\d3-interpolate", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "denis<PERSON>", "url": "https://github.com/denisname"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ledragon"}, {"name": "<PERSON>", "url": "https://github.com/Methuselah96"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for d3-color", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-color", "license": "MIT", "main": "", "name": "@types/d3-color", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-color"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "4144d54b3b9ec40ae81e167958487fa20f6eef03535f11607c0757b8f5d89556", "version": "2.0.6"}