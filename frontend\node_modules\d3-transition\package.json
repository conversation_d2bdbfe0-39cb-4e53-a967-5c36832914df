{"_from": "d3-transition@2", "_id": "d3-transition@2.0.0", "_inBundle": false, "_integrity": "sha512-42ltAGgJesfQE3u9LuuBHNbGrI/AJjNL2OAUdclE70UE6Vy239GCBEYD38uBPoLeNsOhFStGpPI0BAOV+HMxog==", "_location": "/d3-transition", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-transition@2", "name": "d3-transition", "escapedName": "d3-transition", "rawSpec": "2", "saveSpec": null, "fetchSpec": "2"}, "_requiredBy": ["/d3-zoom"], "_resolved": "https://registry.npmjs.org/d3-transition/-/d3-transition-2.0.0.tgz", "_shasum": "366ef70c22ef88d1e34105f507516991a291c94c", "_spec": "d3-transition@2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\d3-zoom", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-transition/issues"}, "bundleDependencies": false, "dependencies": {"d3-color": "1 - 2", "d3-dispatch": "1 - 2", "d3-ease": "1 - 2", "d3-interpolate": "1 - 2", "d3-timer": "1 - 2"}, "deprecated": false, "description": "Animated transitions for D3 selections.", "devDependencies": {"d3-selection": "2", "eslint": "6", "jsdom": "15", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-transition/", "jsdelivr": "dist/d3-transition.min.js", "keywords": ["d3", "d3-module", "dom", "transition", "animation"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-transition.js", "module": "src/index.js", "name": "d3-transition", "peerDependencies": {"d3-selection": "2"}, "repository": {"type": "git", "url": "git+https://github.com/d3/d3-transition.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src"}, "sideEffects": true, "unpkg": "dist/d3-transition.min.js", "version": "2.0.0"}