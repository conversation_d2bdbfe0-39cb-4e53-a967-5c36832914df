#!/usr/bin/env python3
"""
Module de planification pour le scraping automatique de GlobalRoamer
Exécute le scraping toutes les 24 heures
"""

import schedule
import time
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
from globalroamer_scraper import GlobalRoamerScraper
from csv_importer import CSVImporter

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ScrapingScheduler:
    """Classe pour gérer la planification du scraping"""
    
    def __init__(self):
        """Initialiser le planificateur"""
        load_dotenv()
        self.username = os.getenv('GLOBALROAMER_USERNAME')
        self.password = os.getenv('GLOBALROAMER_PASSWORD')
        
        if not self.username or not self.password:
            raise ValueError("Les identifiants GlobalRoamer sont manquants dans le fichier .env")
            
        self.scraper = None
        self.importer = CSVImporter()
        
    def initialize_scraper(self):
        """Initialiser une nouvelle instance du scraper"""
        try:
            self.scraper = GlobalRoamerScraper(
                username=self.username,
                password=self.password
            )
            return True
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'initialisation du scraper: {e}")
            return False
            
    def run_scraping_job(self):
        """Exécuter le job de scraping"""
        try:
            logger.info("🔄 Démarrage du job de scraping planifié")
            start_time = datetime.now()
            
            # Initialiser un nouveau scraper pour chaque exécution
            if not self.initialize_scraper():
                logger.error("❌ Échec de l'initialisation du scraper")
                return
                
            # Test de connectivité
            if not self.scraper.test_connectivity():
                logger.error("❌ Échec du test de connectivité")
                return
                
            # Scraper les données (7 derniers jours par défaut)
            try:
                downloaded_files = self.scraper.scrape_data(days_back=1)  # Seulement le dernier jour
                logger.info(f"📥 Fichiers téléchargés: {downloaded_files}")
                
                # Importer les fichiers dans la base de données
                if downloaded_files:
                    import_results = self.importer.import_multiple_files(downloaded_files)
                    
                    # Log des résultats
                    success_count = sum(1 for result in import_results.values() if result)
                    logger.info(f"✅ {success_count}/{len(downloaded_files)} fichiers importés avec succès")
                else:
                    logger.warning("⚠️ Aucun fichier téléchargé")
                    
            except Exception as e:
                logger.error(f"❌ Erreur pendant le scraping: {e}")
            finally:
                # Nettoyer les ressources
                if hasattr(self.scraper, 'driver') and self.scraper.driver:
                    self.scraper.driver.quit()
                    
            # Calculer la durée d'exécution
            duration = datetime.now() - start_time
            logger.info(f"⏱️ Job terminé en {duration}")
            
        except Exception as e:
            logger.error(f"❌ Erreur générale dans le job: {e}")
            
    def start_scheduler(self):
        """Démarrer le planificateur"""
        try:
            logger.info("🕒 Démarrage du planificateur")
            
            # Planifier le job pour s'exécuter tous les jours à minuit
            schedule.every().day.at("00:00").do(self.run_scraping_job)
            
            # Exécuter le job immédiatement au démarrage
            logger.info("▶️ Première exécution immédiate")
            self.run_scraping_job()
            
            # Boucle principale
            while True:
                schedule.run_pending()
                time.sleep(60)  # Attendre 1 minute avant de vérifier à nouveau
                
        except KeyboardInterrupt:
            logger.info("⏹️ Arrêt du planificateur")
        except Exception as e:
            logger.error(f"❌ Erreur dans le planificateur: {e}")
            
if __name__ == "__main__":
    scheduler = ScrapingScheduler()
    scheduler.start_scheduler() 