declare module 'react-simple-maps' {
  import { ReactNode } from 'react';

  interface Geography {
    rsmKey: string;
    properties: {
      name: string;
      [key: string]: any;
    };
  }

  interface GeographyProps {
    geography: Geography;
    key: string;
    fill?: string;
    stroke?: string;
    style?: {
      default?: any;
      hover?: any;
      pressed?: any;
    };
    onMouseEnter?: () => void;
    onClick?: () => void;
  }

  interface GeographiesProps {
    geography: string;
    children: (props: { geographies: Geography[] }) => ReactNode;
  }

  export const ComposableMap: React.FC<{
    projectionConfig?: {
      scale?: number;
      center?: [number, number];
    };
    children?: ReactNode;
  }>;

  export const ZoomableGroup: React.FC<{
    children?: ReactNode;
  }>;

  export const Geographies: React.FC<GeographiesProps>;
  export const Geography: React.FC<GeographyProps>;
} 