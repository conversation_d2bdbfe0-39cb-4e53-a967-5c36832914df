from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging
import mysql.connector
from mysql.connector import Error
import os
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
load_dotenv()

class KpiAlertsService:
    def __init__(self):
        """Initialise la connexion à la base de données"""
        try:
            self.connection = mysql.connector.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                user=os.getenv('DB_USER', 'root'),
                password=os.getenv('DB_PASSWORD', ''),
                database=os.getenv('DB_NAME', 'kpi')
            )
            logger.info("Connexion à la base de données établie avec succès")
        except Error as e:
            logger.error(f"Erreur de connexion à la base de données: {e}")
            raise

    def get_country_kpis(self, country: str, days: int = 7) -> Dict[str, Any]:
        """Récupère les KPIs pour un pays donné sur une période donnée"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Requête pour obtenir les moyennes des KPIs
            query = """
            SELECT 
                AVG(attach_success_rate) as avg_attach_rate,
                AVG(drop_rate) as avg_drop_rate,
                AVG(latency) as avg_latency,
                COUNT(*) as total_records
            FROM steeringofroaming
            WHERE a_location_country = %s 
            AND timestamp >= DATE_SUB(NOW(), INTERVAL %s DAY)
            """
            
            cursor.execute(query, (country, days))
            result = cursor.fetchone()
            
            if not result or result['avg_attach_rate'] is None:
                return None
                
            return {
                "attach_success": float(result['avg_attach_rate']),
                "drop_rate": float(result['avg_drop_rate'] or 0),
                "latency": float(result['avg_latency'] or 0),
                "sample_size": int(result['total_records'])
            }
            
        except Error as e:
            logger.error(f"Erreur lors de la récupération des KPIs: {e}")
            raise
        finally:
            cursor.close()

    def get_country_alerts(self) -> List[Dict[str, Any]]:
        """Récupère les alertes KPI pour tous les pays"""
        try:
            cursor = self.connection.cursor(dictionary=True)
            
            # Requête pour obtenir les métriques actuelles et précédentes
            query = """
            WITH current_metrics AS (
                SELECT 
                    a_location_country as country,
                    AVG(attach_success_rate) as current_attach_rate,
                    AVG(CASE WHEN drop_rate IS NOT NULL THEN drop_rate ELSE 0 END) as current_drop_rate,
                    AVG(CASE WHEN latency IS NOT NULL THEN latency ELSE 0 END) as current_latency
                FROM steeringofroaming
                WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY a_location_country
            ),
            previous_metrics AS (
                SELECT 
                    a_location_country as country,
                    AVG(attach_success_rate) as prev_attach_rate,
                    AVG(CASE WHEN drop_rate IS NOT NULL THEN drop_rate ELSE 0 END) as prev_drop_rate,
                    AVG(CASE WHEN latency IS NOT NULL THEN latency ELSE 0 END) as prev_latency
                FROM steeringofroaming
                WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 14 DAY)
                    AND timestamp < DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY a_location_country
            )
            SELECT 
                c.country,
                c.current_attach_rate,
                c.current_drop_rate,
                c.current_latency,
                p.prev_attach_rate,
                p.prev_drop_rate,
                p.prev_latency
            FROM current_metrics c
            LEFT JOIN previous_metrics p ON c.country = p.country
            WHERE c.current_attach_rate IS NOT NULL
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            alerts = []
            for row in results:
                current_metrics = {
                    "attach_success": float(row['current_attach_rate']),
                    "drop_rate": float(row['current_drop_rate']),
                    "latency": float(row['current_latency'])
                }
                
                previous_metrics = {
                    "attach_success": float(row['prev_attach_rate'] or 0),
                    "drop_rate": float(row['prev_drop_rate'] or 0),
                    "latency": float(row['prev_latency'] or 0)
                }
                
                # Analyse des anomalies
                alert = self._analyze_metrics(row['country'], current_metrics, previous_metrics)
                if alert:
                    alerts.append(alert)
            
            return alerts
            
        except Error as e:
            logger.error(f"Erreur lors de la récupération des alertes: {e}")
            raise
        finally:
            cursor.close()

    def _analyze_metrics(self, country: str, current: Dict[str, float], previous: Dict[str, float]) -> Optional[Dict[str, Any]]:
        """Analyse les métriques pour détecter les anomalies"""
        # Seuils d'alerte
        ATTACH_RATE_THRESHOLD = 90.0  # %
        DROP_RATE_THRESHOLD = 5.0    # %
        LATENCY_THRESHOLD = 200.0    # ms
        
        # Calcul des variations
        attach_change = ((current["attach_success"] - previous["attach_success"]) / previous["attach_success"]) * 100 if previous["attach_success"] else 0
        drop_change = current["drop_rate"] - previous["drop_rate"]
        latency_change = ((current["latency"] - previous["latency"]) / previous["latency"]) * 100 if previous["latency"] else 0
        
        # Détection des problèmes
        issues = []
        severity = "low"
        
        # Vérification du taux d'attache
        if current["attach_success"] < ATTACH_RATE_THRESHOLD:
            issues.append(f"Taux d'attache faible ({current['attach_success']:.1f}%)")
            severity = "high" if current["attach_success"] < 75 else "medium"
            
        # Vérification du taux de déconnexion
        if current["drop_rate"] > DROP_RATE_THRESHOLD:
            issues.append(f"Taux de déconnexion élevé ({current['drop_rate']:.1f}%)")
            severity = "high" if current["drop_rate"] > 10 else "medium"
            
        # Vérification de la latence
        if current["latency"] > LATENCY_THRESHOLD:
            issues.append(f"Latence élevée ({current['latency']:.0f}ms)")
            severity = "medium"
            
        # Si aucun problème détecté
        if not issues:
            return None
            
        return {
            "country": country,
            "attach_success": current["attach_success"],
            "severity": severity,
            "causes": issues,
            "primary_cause": issues[0],  # La première cause détectée
            "details": {
                "attach_success": {
                    "current": current["attach_success"],
                    "previous_avg": previous["attach_success"],
                    "change_percent": attach_change
                },
                "drop_rate": {
                    "current": current["drop_rate"],
                    "previous_avg": previous["drop_rate"],
                    "change": drop_change
                },
                "latency": {
                    "current": current["latency"],
                    "previous_avg": previous["latency"],
                    "change_percent": latency_change
                }
            }
        }

    def __del__(self):
        """Ferme la connexion à la base de données"""
        if hasattr(self, 'connection') and self.connection.is_connected():
            self.connection.close()
            logger.info("Connexion à la base de données fermée") 