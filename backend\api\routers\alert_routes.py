from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import create_engine, text, func, desc
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import List, Optional
from pydantic import BaseModel
import pandas as pd
import logging
import os
from dotenv import load_dotenv
from ..get_db_connection import get_db_connection

# Charger les variables d'environnement
load_dotenv()

# Configuration du logging
logging.basicConfig(level=os.getenv('LOG_LEVEL', 'INFO'))
logger = logging.getLogger(__name__)

# Créer le router sans préfixe
router = APIRouter()

# Modèle Pydantic pour les alertes
class Alert(BaseModel):
    country: str
    success_rate: float
    failure_rate: float
    type_alert: str
    severity: str
    description: str
    date: str

@router.get("/alerts", response_model=List[Alert])
async def get_alerts():
    """
    Récupère les alertes automatiques basées sur les critères suivants:
    - Taux de succès inférieur à 70% par pays
    - Augmentation des échecs de plus de 30% par rapport à la moyenne des 7 derniers jours
    """
    logger.info("Début de la récupération des alertes")
    
    engine = get_db_connection()
    if not engine:
        logger.error("Impossible de se connecter à la base de données")
        raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
    
    alerts = []
    
    try:
        with engine.connect() as connection:
            logger.info("Exécution de la requête pour les alertes de taux de succès faible")
            # 1. Alertes pour les pays avec un taux de succès inférieur à 70%
            low_success_query = text("""
                SELECT 
                    a_location_country as country,
                    COUNT(CASE WHEN Verdict = 'SUCCESS' OR Verdict = 'PASS' THEN 1 END) as success_count,
                    COUNT(*) as total_count,
                    ROUND(COUNT(CASE WHEN Verdict = 'SUCCESS' OR Verdict = 'PASS' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate,
                    ROUND(COUNT(CASE WHEN Verdict = 'FAIL' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as failure_rate,
                    COUNT(CASE WHEN (Verdict = 'SUCCESS' OR Verdict = 'PASS') AND TCName = 'SteeringOfRoaming' THEN 1 END) as steering_success,
                    COUNT(CASE WHEN TCName = 'SteeringOfRoaming' THEN 1 END) as steering_total
                FROM steeringofroaming
                WHERE
                    TCName = 'SteeringOfRoaming'
                    AND 
                    a_location_country IS NOT NULL 
                    AND a_location_country != ''
                    AND Timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY a_location_country
                HAVING 
                    total_count > 10  -- Ignorer les pays avec trop peu de données
                    AND success_rate < 70
                ORDER BY success_rate ASC
            """)
            
            try:
                result = connection.execute(low_success_query)
                for row in result:
                    logger.debug(f"Alerte trouvée pour le pays {row.country} avec un taux de succès de {row.success_rate}%")
                    alerts.append(Alert(
                        country=row.country,
                        success_rate=float(row.success_rate if row.success_rate is not None else 0),
                        failure_rate=float(row.failure_rate if row.failure_rate is not None else 0),
                        type_alert="low_success_rate",
                        severity="high" if row.success_rate < 50 else "medium",
                        description=f"Taux de succès faible : {row.success_rate}% ({row.success_count}/{row.total_count} succès)",
                        date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    ))
            except Exception as e:
                logger.error(f"Erreur lors de l'exécution de la requête de taux de succès : {str(e)}")
                raise HTTPException(status_code=500, detail=f"Erreur lors de la requête de taux de succès : {str(e)}")
            
            logger.info("Exécution de la requête pour les alertes d'augmentation des échecs")
            # 2. Alertes pour augmentation anormale des échecs
            trend_query = text("""
                WITH daily_stats AS (
                    SELECT 
                        DATE(Timestamp) as date,
                        COALESCE(COUNT(CASE WHEN Verdict = 'FAIL' THEN 1 END), 0) as fail_count,
                        COALESCE(COUNT(*), 0) as total_count
                    FROM steeringofroaming
                    WHERE 
                        Timestamp >= DATE_SUB(NOW(), INTERVAL 8 DAY)
                        AND TCName = 'SteeringOfRoaming'
                    GROUP BY DATE(Timestamp)
                )
                SELECT 
                    COALESCE(ROUND(AVG(CASE 
                        WHEN total_count > 0 THEN (fail_count * 100.0 / total_count)
                        ELSE 0 
                    END), 2), 0) as avg_failure_rate,
                    COALESCE(ROUND(MAX(CASE 
                        WHEN total_count > 0 THEN (fail_count * 100.0 / total_count)
                        ELSE 0 
                    END), 2), 0) as max_failure_rate,
                    MAX(date) as latest_date
                FROM daily_stats
                WHERE date < CURDATE()
            """)
            
            try:
                trend_result = connection.execute(trend_query).fetchone()
                if trend_result and trend_result.max_failure_rate > 0 and trend_result.avg_failure_rate > 0:
                    # Convertir en float pour éviter l'erreur decimal.Decimal * float
                    max_failure_rate = float(trend_result.max_failure_rate)
                    avg_failure_rate = float(trend_result.avg_failure_rate)
                    if max_failure_rate > (avg_failure_rate * 1.3):  # 30% d'augmentation
                        logger.debug(f"Alerte d'augmentation des échecs détectée : {max_failure_rate}% (moyenne : {avg_failure_rate}%)")
                        alerts.append(Alert(
                            country="Global",
                            success_rate=100 - max_failure_rate,
                            failure_rate=max_failure_rate,
                            type_alert="failure_increase",
                            severity="high",
                            description=f"Augmentation anormale des échecs : {trend_result.max_failure_rate}% (moyenne : {trend_result.avg_failure_rate}%)",
                            date=trend_result.latest_date.strftime("%Y-%m-%d %H:%M:%S") if trend_result.latest_date else datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        ))
            except Exception as e:
                logger.error(f"Erreur lors de l'exécution de la requête de tendance : {str(e)}")
                logger.exception("Détails de l'erreur:")
                # Ne pas lever d'exception ici, continuer avec les alertes déjà collectées
            
            logger.info(f"Récupération des alertes terminée. {len(alerts)} alertes trouvées.")
            return alerts
            
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des alertes : {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if engine:
            engine.dispose()
            logger.info("Connexion à la base de données fermée") 