import React from 'react';
import ReactDOM from 'react-dom/client';
import { Toaster } from 'react-hot-toast';
import App from './App';

import './index.css';

// Composant simple de test pour diagnostic
const TestApp = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full">
        <h1 className="text-2xl font-bold mb-4">Test de l'application</h1>
        <p className="mb-4">Si vous voyez ce message, le rendu de base fonctionne correctement.</p>
        <button 
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          onClick={() => {
            // Rediriger vers l'application réelle
            window.location.href = '/app';
          }}
        >
          Aller vers l'application
        </button>
      </div>
    </div>
  )
};

// Composant racine avec le provider d'authentification
const Root = () => {
  return (
    <React.StrictMode>
      <App />
      <Toaster position="top-right" />
    </React.StrictMode>
  );
};

// Monter l'application
const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error('Element root non trouvé');
}

ReactDOM.createRoot(rootElement).render(<Root />);

// Ajouter un script pour masquer la section "Analyse des Échecs"
document.addEventListener('DOMContentLoaded', function() {
  // Fonction pour masquer la section "Analyse des Échecs"
  function hideAnalyseEchecs() {
    // Rechercher tous les éléments qui contiennent "Analyse des Échecs"
    const elements = document.querySelectorAll('h2, h3, div');
    elements.forEach(el => {
      if (el.textContent && el.textContent.includes('Analyse des Échecs')) {
        // Masquer l'élément parent pour cacher toute la section
        const parent = el.closest('.grid') || el.parentElement;
        if (parent) {
          (parent as HTMLElement).style.display = 'none';
        }
      }
    });
    
    // Rechercher les éléments avec les indicateurs "0%", "Données non disponib...", etc.
    const cardElements = document.querySelectorAll('.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4');
    cardElements.forEach(el => {
      if (el.textContent && (
        el.textContent.includes('Taux d\'échec moyen') || 
        el.textContent.includes('Cause principale') || 
        el.textContent.includes('Pays le plus affecté') || 
        el.textContent.includes('Incidents détectés')
      )) {
        (el as HTMLElement).style.display = 'none';
      }
    });
  }
  
  // Exécuter la fonction immédiatement
  hideAnalyseEchecs();
  
  // Exécuter la fonction périodiquement pour s'assurer que les éléments sont masqués
  // même après les mises à jour dynamiques du DOM
  setInterval(hideAnalyseEchecs, 1000);
});
