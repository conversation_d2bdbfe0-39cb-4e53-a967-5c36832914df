#!/usr/bin/env python3
"""
Test simple du scraper GlobalRoamer
"""

import os
import sys
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Ajouter le répertoire parent au path pour importer le scraper
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from globalroamer_scraper import GlobalRoamerScraper

def test_scraper():
    """Test simple du scraper"""
    print("🧪 TEST DU SCRAPER GLOBALROAMER")
    print("=" * 40)
    
    # Récupérer les identifiants
    username = os.getenv('GLOBALROAMER_USERNAME')
    password = os.getenv('GLOBALROAMER_PASSWORD')
    
    if not username or not password:
        print("❌ Identifiants manquants dans .env")
        print("Veuillez configurer GLOBALROAMER_USERNAME et GLOBALROAMER_PASSWORD")
        return False
    
    print(f"👤 Utilisateur: {username}")
    print(f"🔑 Mot de passe: {'*' * len(password)}")
    
    try:
        # Créer le scraper
        scraper = GlobalRoamerScraper(username, password)
        
        # Test de connectivité seulement
        print("\n🔍 Test de connectivité...")
        if scraper.test_connectivity():
            print("✅ Connectivité OK")
            
            # Test de configuration du driver
            print("\n🚗 Test de configuration du driver...")
            driver = scraper.setup_driver()
            if driver:
                print("✅ Driver configuré avec succès")
                
                # Test d'accès à la page de login
                print("\n🌐 Test d'accès à la page de login...")
                try:
                    driver.get(scraper.login_url)
                    print(f"✅ Page chargée - Titre: {driver.title}")
                    print(f"URL: {driver.current_url}")
                except Exception as e:
                    print(f"❌ Erreur d'accès à la page: {e}")
                
                driver.quit()
                return True
            else:
                print("❌ Échec de configuration du driver")
                return False
        else:
            print("❌ Problème de connectivité")
            return False
            
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_scraper()
    if success:
        print("\n🎉 Test réussi!")
    else:
        print("\n💥 Test échoué!")
