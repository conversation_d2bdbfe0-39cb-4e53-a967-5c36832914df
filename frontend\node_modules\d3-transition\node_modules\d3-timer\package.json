{"_from": "d3-timer@1 - 2", "_id": "d3-timer@2.0.0", "_inBundle": false, "_integrity": "sha512-TO4VLh0/420Y/9dO3+f9abDEFYeCUr2WZRlxJvbp4HPTQcSylXNiL6yZa9FIUvV1yRiFufl1bszTCLDqv9PWNA==", "_location": "/d3-transition/d3-timer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "d3-timer@1 - 2", "name": "d3-timer", "escapedName": "d3-timer", "rawSpec": "1 - 2", "saveSpec": null, "fetchSpec": "1 - 2"}, "_requiredBy": ["/d3-transition"], "_resolved": "https://registry.npmjs.org/d3-timer/-/d3-timer-2.0.0.tgz", "_shasum": "055edb1d170cfe31ab2da8968deee940b56623e6", "_spec": "d3-timer@1 - 2", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\d3-transition", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "bugs": {"url": "https://github.com/d3/d3-timer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "An efficient queue capable of managing thousands of concurrent animations.", "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["dist/**/*.js", "src/**/*.js"], "homepage": "https://d3js.org/d3-timer/", "jsdelivr": "dist/d3-timer.min.js", "keywords": ["d3", "d3-module", "timer", "transition", "animation", "requestAnimationFrame", "setTimeout", "setInterval"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/d3-timer.js", "module": "src/index.js", "name": "d3-timer", "repository": {"type": "git", "url": "git+https://github.com/d3/d3-timer.git"}, "scripts": {"postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../${npm_package_name}/dist/${npm_package_name}.js ${npm_package_name}.v${npm_package_version%%.*}.js && cp ../${npm_package_name}/dist/${npm_package_name}.min.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git add ${npm_package_name}.v${npm_package_version%%.*}.js ${npm_package_name}.v${npm_package_version%%.*}.min.js && git commit -m \"${npm_package_name} ${npm_package_version}\" && git push && cd - && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src"}, "sideEffects": false, "unpkg": "dist/d3-timer.min.js", "version": "2.0.0"}