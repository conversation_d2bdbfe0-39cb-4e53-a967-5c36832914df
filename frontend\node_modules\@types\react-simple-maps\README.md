# Installation
> `npm install --save @types/react-simple-maps`

# Summary
This package contains type definitions for react-simple-maps (https://github.com/zcreativelabs/react-simple-maps#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-simple-maps.

### Additional Details
 * Last updated: Mon, 29 Jul 2024 19:36:25 GMT
 * Dependencies: [@types/d3-geo](https://npmjs.com/package/@types/d3-geo), [@types/d3-zoom](https://npmjs.com/package/@types/d3-zoom), [@types/geojson](https://npmjs.com/package/@types/geojson), [@types/react](https://npmjs.com/package/@types/react)

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/thepocp), [<PERSON><PERSON>](https://github.com/pronebird), and [<PERSON><PERSON><PERSON> ](https://github.com/komenank).
