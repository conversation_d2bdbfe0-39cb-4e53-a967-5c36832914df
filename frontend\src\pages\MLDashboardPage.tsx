import React, { useState } from 'react';
import { FaBrain, FaChartLine, FaCog, FaDatabase, FaExclamationTriangle } from 'react-icons/fa';
import MLTrainingPanel from '../components/MLTrainingPanel';

interface AnomalyData {
  timestamp: string;
  severity: string;
  values: Record<string, number>;
  causes: string[];
  severity_score: number;
}

interface AnalysisResult {
  status: string;
  analysis_date: string;
  days_analyzed: number;
  anomalies: AnomalyData[];
}

const MLDashboardPage: React.FC = () => {
  const [analysisResults, setAnalysisResults] = useState<AnalysisResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'training' | 'analysis' | 'monitoring'>('training');

  const runAnalysis = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/kpi/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          days_to_analyze: 1,
        }),
      });

      const result = await response.json();
      
      if (response.ok) {
        setAnalysisResults(result);
      } else {
        setError(result.detail || 'Erreur lors de l\'analyse');
      }
    } catch (err) {
      setError('Erreur de connexion lors de l\'analyse');
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high':
        return <FaExclamationTriangle className="h-4 w-4 text-red-500" />;
      case 'medium':
        return <FaChartLine className="h-4 w-4 text-yellow-500" />;
      default:
        return <FaDatabase className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Tableau de Bord Machine Learning
        </h1>
        <p className="text-gray-600">
          Entraînement et surveillance des modèles de détection d'anomalies KPI
        </p>
      </div>

      {/* Navigation par onglets */}
      <div className="mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('training')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'training'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <FaBrain className="h-4 w-4 mr-2" />
              Entraînement
            </div>
          </button>
          
          <button
            onClick={() => setActiveTab('analysis')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analysis'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <FaChartLine className="h-4 w-4 mr-2" />
              Analyse
            </div>
          </button>
          
          <button
            onClick={() => setActiveTab('monitoring')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'monitoring'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center">
              <FaCog className="h-4 w-4 mr-2" />
              Surveillance
            </div>
          </button>
        </nav>
      </div>

      {/* Contenu des onglets */}
      {activeTab === 'training' && (
        <MLTrainingPanel />
      )}

      {activeTab === 'analysis' && (
        <div className="space-y-6">
          {/* Panel d'analyse */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-800">Analyse des Anomalies</h2>
              <button
                onClick={runAnalysis}
                disabled={loading}
                className={`px-4 py-2 rounded-lg font-medium ${
                  loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Analyse en cours...
                  </div>
                ) : (
                  'Lancer l\'analyse'
                )}
              </button>
            </div>

            {error && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <FaExclamationTriangle className="h-5 w-5 text-red-500 mr-2" />
                  <span className="text-red-700">{error}</span>
                </div>
              </div>
            )}

            {analysisResults && (
              <div>
                <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                  <h3 className="font-semibold text-blue-800 mb-2">Résultats de l'analyse</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Date d'analyse:</span>
                      <div className="font-medium">
                        {new Date(analysisResults.analysis_date).toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <span className="text-gray-600">Jours analysés:</span>
                      <div className="font-medium">{analysisResults.days_analyzed}</div>
                    </div>
                    <div>
                      <span className="text-gray-600">Anomalies détectées:</span>
                      <div className="font-medium text-red-600">
                        {analysisResults.anomalies.length}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Liste des anomalies */}
                {analysisResults.anomalies.length > 0 ? (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Anomalies détectées</h3>
                    {analysisResults.anomalies.map((anomaly, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center">
                            {getSeverityIcon(anomaly.severity)}
                            <span className={`ml-2 px-2 py-1 rounded text-xs font-medium border ${getSeverityColor(anomaly.severity)}`}>
                              {anomaly.severity.toUpperCase()}
                            </span>
                            <span className="ml-2 text-sm text-gray-600">
                              Score: {anomaly.severity_score}
                            </span>
                          </div>
                          <span className="text-sm text-gray-500">
                            {new Date(anomaly.timestamp).toLocaleString()}
                          </span>
                        </div>

                        {/* Valeurs KPI */}
                        <div className="mb-3">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Valeurs KPI:</h4>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                            {Object.entries(anomaly.values).map(([key, value]) => (
                              <div key={key} className="bg-gray-50 p-2 rounded text-xs">
                                <div className="text-gray-600">{key}:</div>
                                <div className="font-medium">{typeof value === 'number' ? value.toFixed(2) : value}</div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Causes */}
                        {anomaly.causes.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Causes possibles:</h4>
                            <div className="flex flex-wrap gap-1">
                              {anomaly.causes.map((cause, causeIndex) => (
                                <span
                                  key={causeIndex}
                                  className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded"
                                >
                                  {cause}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <FaDatabase className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>Aucune anomalie détectée dans la période analysée</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'monitoring' && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">Surveillance Continue</h2>
          <div className="text-center py-8 text-gray-500">
            <FaCog className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>Fonctionnalité de surveillance en temps réel</p>
            <p className="text-sm">À venir dans une prochaine version</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default MLDashboardPage;
