#!/usr/bin/env python3
"""
Test immédiat du scraping (sans attendre 2h du matin)
"""

import os
import sys
import logging
from datetime import datetime

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from automation_service import AutomationService

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_immediate_scraping():
    """Tester le scraping immédiatement"""
    logger.info("🚀 TEST IMMÉDIAT DU SCRAPING")
    logger.info("=" * 50)
    logger.info(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # Créer le service d'automatisation
        automation = AutomationService()
        
        # Exécuter le scraping immédiatement
        logger.info("🕷️ Lancement du scraping immédiat...")
        success = automation.run_scraping_and_import(days_back=1)
        
        if success:
            logger.info("✅ SCRAPING RÉUSSI!")
            
            # Afficher les statistiques
            stats = automation.get_stats()
            logger.info(f"📊 Statistiques mises à jour:")
            logger.info(f"   - Total runs: {stats.get('total_runs', 0)}")
            logger.info(f"   - Successful: {stats.get('successful_runs', 0)}")
            logger.info(f"   - Failed: {stats.get('failed_runs', 0)}")
            logger.info(f"   - Files processed: {stats.get('files_processed', 0)}")
            
        else:
            logger.error("❌ SCRAPING ÉCHOUÉ")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Erreur lors du test: {e}")
        return False

def main():
    """Fonction principale"""
    logger.info("🧪 TEST IMMÉDIAT DU SYSTÈME DE SCRAPING")
    logger.info("=" * 60)
    
    success = test_immediate_scraping()
    
    if success:
        logger.info("🎉 TEST RÉUSSI - Le système fonctionne parfaitement!")
        logger.info("💡 Vous pouvez maintenant laisser start_automation.py tourner 24h/24")
    else:
        logger.warning("⚠️ Test échoué - Vérifiez la configuration")
    
    return success

if __name__ == "__main__":
    main()
