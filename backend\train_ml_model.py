#!/usr/bin/env python3
"""
Script de démarrage pour l'entraînement du modèle ML
Peut être exécuté en ligne de commande ou programmé avec cron
"""

import sys
import os
import argparse
import logging
from datetime import datetime

# Ajouter le répertoire du projet au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from api.ml.train_model import ModelTrainer

def setup_logging(log_level='INFO'):
    """Configure le logging"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('ml_training.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(
        description="Script d'entraînement du modèle ML pour la détection d'anomalies KPI"
    )
    parser.add_argument(
        '--days', 
        type=int, 
        default=90, 
        help="Nombre de jours de données historiques (défaut: 90)"
    )
    parser.add_argument(
        '--test-size', 
        type=float, 
        default=0.2, 
        help="Proportion des données pour le test (défaut: 0.2)"
    )
    parser.add_argument(
        '--force', 
        action='store_true', 
        help="Forcer l'entraînement même si un modèle existe"
    )
    parser.add_argument(
        '--log-level', 
        default='INFO', 
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help="Niveau de logging (défaut: INFO)"
    )
    parser.add_argument(
        '--auto', 
        action='store_true', 
        help="Mode automatique (pas d'interaction utilisateur)"
    )
    
    args = parser.parse_args()
    
    # Configuration du logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    logger.info("🤖 DÉBUT DE L'ENTRAÎNEMENT DU MODÈLE ML")
    logger.info("=" * 60)
    logger.info(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"📊 Jours de données: {args.days}")
    logger.info(f"🧪 Taille du test: {args.test_size}")
    logger.info(f"🔄 Force retrain: {args.force}")
    logger.info("=" * 60)
    
    try:
        # Créer le trainer
        trainer = ModelTrainer()
        
        # Vérifier si un modèle existe déjà
        if not args.force and trainer.model.load_models():
            message = "⚠️ Un modèle existe déjà."
            if args.auto:
                logger.info(f"{message} Mode automatique: arrêt.")
                return 0
            else:
                response = input(f"{message} Continuer quand même? (y/N): ")
                if response.lower() not in ['y', 'yes', 'oui']:
                    logger.info("Entraînement annulé par l'utilisateur.")
                    return 0
        
        # Préparer les données
        logger.info("📊 Préparation des données...")
        df = trainer.prepare_training_data(args.days)
        
        if df.empty:
            logger.error("❌ Aucune donnée disponible pour l'entraînement")
            return 1
        
        logger.info(f"✅ {len(df)} enregistrements préparés")
        
        # Entraîner et évaluer
        logger.info("🚀 Début de l'entraînement...")
        results = trainer.train_and_evaluate(df, args.test_size)
        
        # Afficher les résultats
        logger.info("🎉 RÉSULTATS D'ENTRAÎNEMENT:")
        logger.info("-" * 40)
        logger.info(f"📊 Données d'entraînement: {results['training_data_size']:,} enregistrements")
        logger.info(f"📊 Données de test: {results['test_data_size']:,} enregistrements")
        logger.info(f"⏱️ Temps d'entraînement: {results['training_time_seconds']:.2f} secondes")
        logger.info(f"🔍 Anomalies détectées: {results['anomalies_detected']}")
        
        if 'evaluation_metrics' in results:
            metrics = results['evaluation_metrics']
            logger.info(f"📈 Taux d'anomalies: {metrics['anomaly_rate_percent']}%")
            logger.info(f"🤝 Taux de consensus: {metrics['consensus_rate_percent']}%")
        
        logger.info("✅ Entraînement terminé avec succès!")
        return 0
        
    except KeyboardInterrupt:
        logger.info("⚠️ Entraînement interrompu par l'utilisateur")
        return 1
    except Exception as e:
        logger.error(f"💥 Erreur fatale: {e}")
        if args.log_level == 'DEBUG':
            import traceback
            logger.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
