{"_from": "@types/react-simple-maps", "_id": "@types/react-simple-maps@3.0.6", "_inBundle": false, "_integrity": "sha512-hR01RXt6VvsE41FxDd+Bqm1PPGdKbYjCYVtCgh38YeBPt46z3SwmWPWu2L3EdCAP6bd6VYEgztucihRw1C0Klg==", "_location": "/@types/react-simple-maps", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "@types/react-simple-maps", "name": "@types/react-simple-maps", "escapedName": "@types%2freact-simple-maps", "scope": "@types", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#DEV:/", "#USER"], "_resolved": "https://registry.npmjs.org/@types/react-simple-maps/-/react-simple-maps-3.0.6.tgz", "_shasum": "96728a17d3808cc17072db687b083872dc24fb26", "_spec": "@types/react-simple-maps", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/thepocp"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/pronebird"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/komenank"}], "dependencies": {"@types/d3-geo": "^2", "@types/d3-zoom": "^2", "@types/geojson": "*", "@types/react": "*"}, "deprecated": false, "description": "TypeScript definitions for react-simple-maps", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-simple-maps", "license": "MIT", "main": "", "name": "@types/react-simple-maps", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-simple-maps"}, "scripts": {}, "typeScriptVersion": "4.8", "types": "index.d.ts", "typesPublisherContentHash": "03085fd045556def09e0701ed68df4b590576bc95fdaf55295fd3455374397cc", "version": "3.0.6"}