from typing import Dict, List, Any
import pandas as pd
from sqlalchemy import create_engine, text
from ..ml.kpi_predictor import K<PERSON><PERSON>nomalyDetector
from datetime import datetime, timedelta
from ..get_db_connection import get_db_connection

class MLService:
    def __init__(self):
        self.model = KPIAnomalyDetector()
        self.is_trained = False
        self.engine = get_db_connection()

    def get_training_data(self, days_back: int = 30) -> pd.DataFrame:
        """Récupère les données d'entraînement depuis la base de données"""
        query = """
        SELECT 
            Timestamp,
            Verdict,
            Success,
            Failure,
            a_LupDuration,
            a_NrOfLupRequests,
            a_NrOfPlmnsRejected,
            a_networkType,
            a_location_country,
            CASE 
                WHEN Verdict = 'PASS' THEN 1 
                WHEN Verdict = 'FAIL' THEN 0 
                ELSE 0.5 
            END as verdict_score,
            CASE
                WHEN a_LupDuration IS NOT NULL THEN 
                    CAST(TIME_TO_SEC(a_LupDuration) AS FLOAT)
                ELSE NULL
            END as lup_duration_seconds
        FROM steeringofroaming
        WHERE Timestamp >= DATE_SUB(NOW(), INTERVAL :days_back DAY)
        AND TCName = 'SteeringOfRoaming'
        ORDER BY Timestamp
        """
        
        try:
            df = pd.read_sql(
                text(query), 
                self.engine, 
                params={"days_back": days_back}
            )
            
            # Prétraitement des données
            df['Timestamp'] = pd.to_datetime(df['Timestamp'])
            df = df.set_index('Timestamp')
            
            # Calculer des KPIs dérivés
            df['success_rate'] = df['Success'].rolling('1D').mean()
            df['avg_lup_duration'] = df['lup_duration_seconds'].rolling('1D').mean()
            df['failure_rate'] = df['Failure'].rolling('1D').mean()
            
            # Remplacer les valeurs manquantes par la moyenne
            numeric_columns = df.select_dtypes(include=['float64', 'int64']).columns
            df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].mean())
            
            return df
            
        except Exception as e:
            print(f"Erreur lors de la récupération des données: {str(e)}")
            raise

    def train_model(self, days_back: int = 30):
        """Entraîne le modèle avec les données historiques de la base de données"""
        try:
            # Récupérer les données d'entraînement
            df = self.get_training_data(days_back)
            
            # Entraîner le modèle
            self.model.train(df)
            self.is_trained = True
            return {"status": "success", "message": "Modèle entraîné avec succès"}
        except Exception as e:
            return {"status": "error", "message": f"Erreur lors de l'entraînement: {str(e)}"}

    def analyze_kpis(self, days_to_analyze: int = 1, thresholds: Dict[str, float] = None) -> List[Dict[str, Any]]:
        """Analyse les KPIs avec le modèle ML"""
        if not self.is_trained and not self.model.load_models():
            raise Exception("Le modèle n'est pas entraîné")

        # Définir des seuils par défaut si non fournis
        default_thresholds = {
            "success_rate": 0.85,  # 85% minimum
            "avg_lup_duration": 5.0,  # 5 secondes maximum
            "failure_rate": 0.15    # 15% maximum
        }
        thresholds = thresholds or default_thresholds

        try:
            # Récupérer les données récentes à analyser
            current_data = self.get_training_data(days_to_analyze)
            
            # Détecter les anomalies avec le modèle ML
            anomalies = self.model.predict_anomalies(current_data)

            # Enrichir les résultats avec des informations métier
            enriched_anomalies = []
            for anomaly in anomalies:
                kpi_values = anomaly["values"]
                
                # Analyser chaque KPI par rapport aux seuils
                kpi_analysis = []
                for kpi_name, value in kpi_values.items():
                    if kpi_name in thresholds:
                        threshold = thresholds[kpi_name]
                        is_degraded = (
                            value < threshold if kpi_name == "success_rate"
                            else value > threshold
                        )
                        if is_degraded:
                            kpi_analysis.append({
                                "kpi_name": kpi_name,
                                "current_value": value,
                                "threshold": threshold,
                                "degradation_percent": abs((value - threshold) / threshold) * 100
                            })

                if kpi_analysis:
                    enriched_anomaly = {
                        "timestamp": anomaly["timestamp"],
                        "severity": anomaly["severity"],
                        "affected_kpis": kpi_analysis,
                        "prediction_error": anomaly["prediction_error"],
                        "recommendations": self.generate_recommendations(kpi_analysis)
                    }
                    enriched_anomalies.append(enriched_anomaly)

            return enriched_anomalies
            
        except Exception as e:
            print(f"Erreur lors de l'analyse des KPIs: {str(e)}")
            raise

    def generate_recommendations(self, kpi_analysis: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """Génère des recommandations basées sur les KPIs dégradés"""
        recommendations = []
        
        for kpi in kpi_analysis:
            kpi_name = kpi["kpi_name"]
            degradation = kpi["degradation_percent"]
            
            if kpi_name == "success_rate":
                if degradation > 20:
                    recommendations.append({
                        "priority": "high",
                        "action": "Vérifier immédiatement la configuration du steering",
                        "expected_impact": "Amélioration rapide du taux de succès"
                    })
                else:
                    recommendations.append({
                        "priority": "medium",
                        "action": "Analyser les patterns d'échec",
                        "expected_impact": "Identification des causes principales"
                    })
                    
            elif kpi_name == "avg_lup_duration":
                if degradation > 30:
                    recommendations.append({
                        "priority": "high",
                        "action": "Optimiser les temps de réponse du réseau",
                        "expected_impact": "Réduction des temps de latence"
                    })
                    
            elif kpi_name == "failure_rate":
                if degradation > 25:
                    recommendations.append({
                        "priority": "high",
                        "action": "Analyser les logs d'erreur en détail",
                        "expected_impact": "Réduction du taux d'échec"
                    })

        return recommendations 