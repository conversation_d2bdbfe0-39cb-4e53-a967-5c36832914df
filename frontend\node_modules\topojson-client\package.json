{"_from": "topojson-client@^3.1.0", "_id": "topojson-client@3.1.0", "_inBundle": false, "_integrity": "sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==", "_location": "/topojson-client", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "topojson-client@^3.1.0", "name": "<PERSON><PERSON><PERSON><PERSON>-client", "escapedName": "<PERSON><PERSON><PERSON><PERSON>-client", "rawSpec": "^3.1.0", "saveSpec": null, "fetchSpec": "^3.1.0"}, "_requiredBy": ["/react-simple-maps"], "_resolved": "https://registry.npmjs.org/topojson-client/-/topojson-client-3.1.0.tgz", "_shasum": "22e8b1ed08a2b922feeb4af6f53b6ef09a467b99", "_spec": "topojson-client@^3.1.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\react-simple-maps", "author": {"name": "<PERSON>", "url": "https://bost.ocks.org/mike"}, "bin": {"topo2geo": "bin/topo2geo", "topomerge": "bin/topomerge", "topoquantize": "bin/topoquantize"}, "bugs": {"url": "https://github.com/topojson/topojson-client/issues"}, "bundleDependencies": false, "dependencies": {"commander": "2"}, "deprecated": false, "description": "Manipulate TopoJSON and convert it to GeoJSON.", "devDependencies": {"eslint": "6", "rollup": "1", "rollup-plugin-terser": "5", "tape": "4"}, "files": ["bin/topo*", "dist/**/*.js", "src/**/*.js"], "homepage": "https://github.com/topojson/topojson-client", "jsdelivr": "dist/topojson-client.min.js", "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "topology", "g<PERSON><PERSON><PERSON>"], "license": "ISC", "main": "dist/topojson-client.js", "module": "src/index.js", "name": "<PERSON><PERSON><PERSON><PERSON>-client", "repository": {"type": "git", "url": "git+https://github.com/topojson/topojson-client.git"}, "scripts": {"postpublish": "git push && git push --tags && zip -j dist/${npm_package_name}.zip -- LICENSE README.md dist/${npm_package_name}.js dist/${npm_package_name}.min.js", "prepublishOnly": "rm -rf dist && yarn test", "pretest": "rollup -c", "test": "tape 'test/**/*-test.js' && eslint src test"}, "unpkg": "dist/topojson-client.min.js", "version": "3.1.0"}