{"_from": "react-leaflet@^5.0.0", "_id": "react-leaflet@5.0.0", "_inBundle": false, "_integrity": "sha512-CWbTpr5vcHw5bt9i4zSlPEVQdTVcML390TjeDG0cK59z1ylexpqC6M1PJFjV8jD7CF+ACBFsLIDs6DRMoLEofw==", "_location": "/react-leaflet", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "react-leaflet@^5.0.0", "name": "react-leaflet", "escapedName": "react-leaflet", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/react-leaflet/-/react-leaflet-5.0.0.tgz", "_shasum": "945d40bad13b69e8606278b19446b00bab57376a", "_spec": "react-leaflet@^5.0.0", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/PaulLeCam/react-leaflet/issues"}, "bundleDependencies": false, "dependencies": {"@react-leaflet/core": "^3.0.0"}, "deprecated": false, "description": "React components for Leaflet maps", "devDependencies": {"@types/geojson": "^7946.0.15", "@types/leaflet": "^1.9.15", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2"}, "exports": {".": "./lib/index.js", "./*": "./lib/*.js"}, "files": ["lib/*"], "homepage": "https://react-leaflet.js.org", "jest": {"extensionsToTreatAsEsm": [".ts", ".tsx"], "resolver": "ts-jest-resolver", "testEnvironment": "jsdom", "transform": {"^.+\\.(t|j)sx?$": ["@swc/jest", {"root": "../.."}]}}, "keywords": ["react-component", "react", "leaflet", "map"], "license": "Hippocratic-2.1", "main": "lib/index.js", "name": "react-leaflet", "peerDependencies": {"leaflet": "^1.9.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/PaulLeCam/react-leaflet.git"}, "scripts": {"build": "pnpm run build:clean && pnpm run build:types && pnpm run build:js", "build:clean": "del lib", "build:js": "swc src -d ./lib --config-file ../../.swcrc --strip-leading-paths", "build:types": "tsc --emitDeclarationOnly", "start": "pnpm run test && pnpm run build", "test": "pnpm run test:types && pnpm run test:unit", "test:types": "tsc --noEmit", "test:unit": "cross-env NODE_ENV=test jest"}, "sideEffects": false, "type": "module", "types": "lib/index.d.ts", "version": "5.0.0"}