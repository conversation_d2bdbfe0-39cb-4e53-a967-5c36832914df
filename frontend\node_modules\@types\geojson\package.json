{"_from": "@types/geojson@*", "_id": "@types/geojson@7946.0.16", "_inBundle": false, "_integrity": "sha512-6C8nqWur3j98U6+lXDfTUWIfgvZU+EumvpHKcYjujKH7woYyLj2sUmff0tRhrqM7BohUw7Pz3ZB1jj2gW9Fvmg==", "_location": "/@types/geojson", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/geojson@*", "name": "@types/geojson", "escapedName": "@types%2f<PERSON><PERSON><PERSON><PERSON>", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/leaflet"], "_resolved": "https://registry.npmjs.org/@types/geojson/-/geojson-7946.0.16.tgz", "_shasum": "8ebe53d69efada7044454e3305c19017d97ced2a", "_spec": "@types/geojson@*", "_where": "C:\\Users\\<USER>\\OneDrive\\Bureau\\projet_organise\\frontend\\node_modules\\@types\\leaflet", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/cobster"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/atd-schubert"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/icholy"}, {"name": "<PERSON>", "url": "https://github.com/danvk"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for geojson", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/geojson", "license": "MIT", "main": "", "name": "@types/geojson", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/geojson"}, "scripts": {}, "typeScriptVersion": "5.0", "types": "index.d.ts", "typesPublisherContentHash": "e7997f4827a9a92b60c7a6cb27e8f18fa760803e9dd021965e95604338b72e88", "version": "7946.0.16"}