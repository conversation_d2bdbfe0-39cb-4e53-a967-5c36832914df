#!/usr/bin/env python3
"""
Test complet du processus de scraping et d'import
"""

import os
import sys
from dotenv import load_dotenv

# Charger les variables d'environnement
env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
load_dotenv(env_path)

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from automation_service import AutomationService

def test_full_process():
    """Test complet du processus"""
    print("🧪 TEST COMPLET DU PROCESSUS DE SCRAPING")
    print("=" * 50)
    
    try:
        # Créer le service d'automatisation
        service = AutomationService()
        
        print(f"Username: {service.username}")
        print(f"Password: {'*' * len(service.password) if service.password else 'None'}")
        
        # Exécuter le processus
        print("\n🚀 Exécution du processus...")
        result = service.run_scraping_and_import(days_back=1)
        
        if result:
            print("✅ Processus terminé avec succès!")
            
            # Afficher les statistiques
            stats = service.get_status()
            print("\n📊 Statistiques:")
            print(f"- Total runs: {stats.get('total_runs', 0)}")
            print(f"- Successful runs: {stats.get('successful_runs', 0)}")
            print(f"- Failed runs: {stats.get('failed_runs', 0)}")
            print(f"- Files processed: {stats.get('files_processed', 0)}")
            
        else:
            print("❌ Processus échoué!")
            
        return result
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_process()
    print(f"\n{'🎉 TEST RÉUSSI' if success else '💥 TEST ÉCHOUÉ'}")
